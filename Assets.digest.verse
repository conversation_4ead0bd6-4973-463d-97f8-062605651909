# Copyright Epic Games, Inc. All Rights Reserved.
#################################################
# Generated Digest of Verse API
# DO NOT modify this manually!
# Generated from build: ++Fortnite+Release-35.10-CL-42906078
#################################################



    <#> AnimatingBackground_material was not created.

    <#> Heart_material was not created.

    <#> Like_material was not created.

    <#> Textes_material was not created.

        <#> M_Arcade_Play_material was not created.

        <#> M_ATM_Screen_material was not created.

        <#> M_emission_white_material was not created.

        <#> M_Racetrack_Barrier_Custom_Tex_material was not created.

        <#> M_Screen_OFF_material was not created.

        <#> M_Screen_ON_material was not created.

        <#> M_finish_line_material was not created.

            <#> M_Silver_material was not created.

        <#> M_beam_material was not created.

        <#> M_coal_material was not created.

        <#> M_emission_static_material was not created.

        <#> M_Gem_material was not created.

        <#> M_neon_material was not created.

        <#> M_neon_2_material was not created.

        <#> M_teddy_bear_heart_material was not created.

        <#> M_Glass_VFX_material was not created.

    <#> graffititest_Mat_material was not created.

    <#> PekoCoin_material was not created.

    <#> TransparentMaterial_material was not created.

        <#> Admaterial_material was not created.

        <#> SecondAd_material was not created.

        <#> BreathingRedlight_Mat_material was not created.

        <#> Cloudmaterial_material was not created.

            <#> Blue_4_material was not created.

            <#> Brown_dark_4_material was not created.

            <#> Brown_light_material was not created.

            <#> Green_light_2_material was not created.

            <#> Orange_7_material was not created.

            <#> Pink_material was not created.

            <#> Red_dark_material was not created.

            <#> Yellow_light_3_material was not created.

        <#> FakeMirror_mat_material was not created.

        <#> Explosion_material was not created.

        <#> Piece_material was not created.

        <#> Smoke_material was not created.

        <#> Baseball_glove_Inst_material was not created.

        <#> BlackDot_Inst_material was not created.

        <#> Basketball_ball_Inst_material was not created.

        <#> Leafs_Inst_material was not created.

        <#> M_w_Texture_two_sided_material was not created.

        <#> Measures_Inst_material was not created.

        <#> Popcorn_Inst_material was not created.

        <#> Whistle_Inst_material was not created.

        <#> BronzeOreMaterial_material was not created.

        <#> GoldOreMat_material was not created.

        <#> IronOreMat_material was not created.

        <#> Woldbarriermat_material was not created.

    <#> prezent_BaseColor_Mat_material was not created.

        <#> texture_Mat_material was not created.

            <#> rotatingobj_mat_material was not created.

        <#> PRZYCISK_001_material was not created.

        <#> PRZYCISK_002_Blue_material was not created.

        <#> PRZYCISK_002_GREEN_material was not created.

            <#> texture_Mat_material was not created.

            <#> texture_Mat_material was not created.

            <#> TEX_Texture_TopHat_Mat_material was not created.

            <#> texture_Mat_material was not created.

        <#> DROPPER_material was not created.

        <#> TASMA1_material was not created.

            <#> LOGO_GENERALI_material was not created.

            <#> COIN1_material was not created.

            <#> Material_011_material was not created.

            <#> LOGO1_material was not created.

            <#> NeonRed_material was not created.

            <#> ZUBR1_material was not created.

            <#> M_Atlas_material was not created.

        <#> BG_material was not created.
             Some parameters from BG were skipped due to being an invalid identifier: 
                 'Player Color'


        <#> FORSALE_material was not created.

        <#> PlayerColor1_material was not created.
             Some parameters from PlayerColor1 were skipped due to being an invalid identifier: 
                 'Player Color'


        <#> PlayerColor2_material was not created.
             Some parameters from PlayerColor2 were skipped due to being an invalid identifier: 
                 'Player Color'


        <#> PlayerColor3_material was not created.
             Some parameters from PlayerColor3 were skipped due to being an invalid identifier: 
                 'Player Color'


        <#> PlayerColorNone_material was not created.
             Some parameters from PlayerColorNone were skipped due to being an invalid identifier: 
                 'Player Color'


        <#> M_CashCoin_material was not created.

        <#> M_CashCoin_Inst_material was not created.

        <#> M_CashCoinn_material was not created.

        <#> NewMaterial_material was not created.

        <#> STRZALKA_Base_color_Mat_material was not created.

        <#> DISPLAY_material was not created.

        <#> EKRAN1_material was not created.

    <#> fakeislad_Mat_material was not created.

    <#> pekao_pion_cmyk_Mat_material was not created.

    <#> PekoLogoPoz_Mat_material was not created.

        <#> _5000__Mat_material was not created.

        <#> boss_Mat_material was not created.

        <#> ChooseBoss_ENG_Mat_material was not created.

        <#> Delete_Mat_material was not created.

        <#> plant_Mat_material was not created.

        <#> ScreenShot00086_Mat_material was not created.

        <#> ScreenShot00087_Mat_material was not created.

        <#> ScreenShot00088_Mat_material was not created.

        <#> Arrow_Graphic__1__Mat_material was not created.

        <#> buy_Mat_material was not created.

        <#> claim_Mat_material was not created.

        <#> Creative_Coin_Mat_material was not created.

        <#> LEVELUP_Mat_material was not created.

        <#> M_100b_Mat_material was not created.

        <#> M_200_Mat_material was not created.

        <#> M_500b_Mat_material was not created.

        <#> M_50_Mat_material was not created.

        <#> M_Cloud_material was not created.

        <#> M_Money_material was not created.

        <#> Upgrade_Mat_material was not created.

        <#> T_Clouds_02_Mat_material was not created.

            <#> arrow_Mat_material was not created.

            <#> DollarSign_Mat_material was not created.

            <#> T_Mask_1_Mat_material was not created.

            <#> M_chest_large_col_material was not created.

            <#> White4x4_Mat_material was not created.

            <#> Invisible_M_material was not created.

            <#> M_RotateToPlayerText_material was not created.

            <#> eng_Mat_material was not created.

            <#> logopekao_Mat_material was not created.

<#>
 Some assets were skipped due to errors: 

    Invalid asset names: 
        '28349_Industrial_machine_working_loop-full' in module KubaModele.Crushing_Machine
        'Laser_ODY-1573-028' in module KubaModele.Laser
        '4Note_Single' in module Materials.FX
        '8Note_Double' in module Materials.FX
        '8Note_Single' in module Materials.FX
        '05959_slow_rotating_helicopter_loop' in module Sequences.HeliStart
        'helicopter-starting-exterior-01' in module Sequences.HeliStart
        '30968_Pressure_machine_press_3-full' in module Sounds.Dropper
        'Is_It_Hip-Hop' in module Sounds.Music.HipHopRadio
        'Niki_N__Phaser_-_LoFi_90s' in module Sounds.Music.LoFiradio
        'Fashion_Stylist__no-vocals_' in module Sounds.Music.MusicInBank
        'File_A_2-12_GreenRocketAudio_-_Funk__Full_Version_' in module Sounds.Music.MusicInBank
        'ASX_RED_45625507_Run-It-Up-inst' in module Sounds.Music.RaceMusic
        'ASX_WTG_51641610_Modern-Day-Royalty-inst' in module Sounds.Music.RaceMusic
        'do' in module Sounds.SFX.Pianio
        '100Coin_Mat' in module TycoonPack.Material
        '10Coin_Mat' in module TycoonPack.Material
        '20Coin_Mat' in module TycoonPack.Material
        '25Coin_Mat' in module TycoonPack.Material
        '500Coin_Mat' in module TycoonPack.Material
        '50Coin_Mat' in module TycoonPack.Material
        '5Coin_Mat' in module TycoonPack.Material
        '75Coin_Mat' in module TycoonPack.Material


using {/UnrealEngine.com/Temporary/UI}
using {/Verse.org/Colors}
using {/Verse.org/Simulation}
using {/Verse.org/Assets}
M_FortniteBase_Parent_Inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
    @editable
    var FN_DestructibeDamage_CrackPreview<public>:float = external {}

    @editable
    var FN_DestructibeDamage_CrackSize<public>:float = external {}

    @editable
    var FN_DestructibeDamage_CrackColorBrightness<public>:float = external {}

    @editable
    var WSDamageRadius<public>:float = external {}

    @editable
    var MaxDistSqr<public>:float = external {}

    @editable
    var Code_DestructionAmount<public>:float = external {}

    @editable
    var WSDamageRadiusAlt<public>:float = external {}

    @editable
    var DestructionMultiplier<public>:float = external {}

    @editable
    var CurieCharred<public>:float = external {}

    @editable
    var CurieSphericalMappingScale<public>:float = external {}

    @editable
    var CurieElementIndex<public>:float = external {}

    @editable
    var CurieLERP<public>:float = external {}

    @editable
    var Xscale<public>:float = external {}

    @editable
    var FlameGlowStrength_Low<public>:float = external {}

    @editable
    var CurieChared<public>:float = external {}

    @editable
    var BlendToIntermediate<public>:float = external {}

    @editable
    var EpicFlamesExponent<public>:float = external {}

    @editable
    var EpicFlamesScale<public>:float = external {}

    @editable
    var FlamesDivisorB<public>:float = external {}

    @editable
    var FlamesDivisorA<public>:float = external {}

    @editable
    var HeightFalloffRadius<public>:float = external {}

    @editable
    var HeightFalloffHardness<public>:float = external {}

    @editable
    var TextureTile_U<public>:float = external {}

    @editable
    var TextureTile_V<public>:float = external {}

    @editable
    var Emissive_Multiply<public>:float = external {}

    @editable
    var Roughness_Minimum<public>:float = external {}

    @editable
    var Roughness_Maximum<public>:float = external {}

    @editable
    var Specular_Minimum<public>:float = external {}

    @editable
    var Specular_Maximum<public>:float = external {}

    @editable
    var Metallic_Minimum<public>:float = external {}

    @editable
    var Metallic_Maximum<public>:float = external {}

    @editable
    var WorldAlignedTextures_ProjectionTransitionSharpness<public>:float = external {}

    @editable
    var NormalMap_Intensity<public>:float = external {}

    @editable
    var OpacityMask_Minimum<public>:float = external {}

    @editable
    var OpacityMask_Maximum<public>:float = external {}

    @editable
    var DetailTexture_ProjectionTransitionSharpness<public>:float = external {}

    @editable
    var DetailTexture_Scale<public>:float = external {}

    @editable
    var DetailTexture_BaseColor_Minimum<public>:float = external {}

    @editable
    var DetailTexture_BaseColor_Maximum<public>:float = external {}

    @editable
    var DetailTexture_Mask_Power<public>:float = external {}

    @editable
    var DetailTexture_Mask_Add<public>:float = external {}

    @editable
    var DetailTexture_Mask_InvertDotProduct<public>:float = external {}

    @editable
    var AmbientOcclusion_Minimum<public>:float = external {}

    @editable
    var AmbientOcclusion_Maximum<public>:float = external {}

    @editable
    var AmbientOcclusion_Power<public>:float = external {}

    @editable
    var AmbientOcclusion_Specular_Amount<public>:float = external {}

    @editable
    var AmbientOcclusion_BaseColor_Amount<public>:float = external {}

    @editable
    var NormalFlatness<public>:float = external {}

    @editable
    var UseWorldAlignedTextures<public>:float = external {}

    @editable
    var UseDetailTexture<public>:float = external {}

    @editable
    var WSDeformationCenter<public>:color = external {}

    @editable
    var Code_PrecomputedHitVector<public>:color = external {}

    @editable
    var Code_PrecomputedHitVector_Prev_Frame<public>:color = external {}

    @editable
    var WSDeformationCenterAlt<public>:color = external {}

    @editable
    var EmissionColor<public>:color = external {}

    @editable
    var BlueprintColor<public>:color = external {}

    @editable
    var CuriePanSpeedTerrainWSv4<public>:color = external {}

    @editable
    var BaseColor_Tint<public>:color = external {}

    @editable
    var Emissive_ColorTint<public>:color = external {}

    @editable
    var WorldAlignedTextures_Size<public>:color = external {}

    @editable
    var BaseColor_Divide<public>:color = external {}

    @editable
    var DetailTexture_NormalMapStrength<public>:color = external {}

    @editable
    var DetailTexture_Mask_DotProduct<public>:color = external {}

    @editable
    var AmbientOcclusionChannel<public>:color = external {}

    @editable
    var SpecularChannel<public>:color = external {}

    @editable
    var RoughnessChannel<public>:color = external {}

    @editable
    var MetallicChannel<public>:color = external {}

    @editable
    var OpacityMask_TextureChannel<public>:color = external {}

    var BaseColor_Texture<public>:texture = external {}

    var NormalMap_Texture<public>:texture = external {}

    var Emissive_Texture<public>:texture = external {}

    var OpacityMask_Texture<public>:texture = external {}

    var DetailTexture_Diffuse<public>:texture = external {}

    var DetailTexture_Normal<public>:texture = external {}

M_FortniteBase_Parent_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Blueprints
(/<EMAIL>/Pekao_Bank:)Blueprints := module:
    # Module import path: /<EMAIL>/Pekao_Bank/Blueprints/Coin
    (/<EMAIL>/Pekao_Bank/Blueprints:)Coin := module:
        (/<EMAIL>/Pekao_Bank/Blueprints/Coin:)COIN_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Param<public>:color = external {}

        (/<EMAIL>/Pekao_Bank/Blueprints/Coin:)COIN<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Blueprints/Coin:)DropperCoin<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Blueprints/Coin2
    Coin2 := module:
        (/<EMAIL>/Pekao_Bank/Blueprints/Coin2:)COIN_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Param<public>:color = external {}

        (/<EMAIL>/Pekao_Bank/Blueprints/Coin2:)COIN<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Blueprints/Coin2:)DropperCoin<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Blueprints/Coin3
    Coin3 := module:
        (/<EMAIL>/Pekao_Bank/Blueprints/Coin3:)COIN_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Param<public>:color = external {}

        (/<EMAIL>/Pekao_Bank/Blueprints/Coin3:)COIN<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        DropperCoin3<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

# Module import path: /<EMAIL>/Pekao_Bank/FX
(/<EMAIL>/Pekao_Bank:)FX := module:
    Ballhit_Big_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Ballhit_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Ballhit_NE_System_2<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Basketball_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Blow_up_Canister_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Blow_up_LighterNE_System1<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Confetti_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Glove_Catch_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    HotDog_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    LAB_Bubble_Flask10_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    LAB_Bubble_Flask1_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    LAB_Bubble_Flask2_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    LAB_Bubble_Flask5_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Leafs_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Leafs_NE_System1<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Music_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Music_NE_SystemNoLoop<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Plane_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    PlayerDeadbyFallingObject_NE<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Popcorn_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Puck_Desapear_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Salt_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Snow_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Snow_NE_System1<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Steam_L_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Steam_M_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Steam_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Steam_S_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Steam_Small_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Steam_XL_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Steam_XL_NE_System1<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Suck_In_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Suck_Out_2_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Suck_Out_Gloves_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Suck_Out_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Suck_Out_NE_System_Bowling<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Sugar_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Sugar_NE_System_2<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Transformation_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Transformation_NE_System1<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Water_splash_small_splash_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Water_splash_small_splash_NE_System1<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Waterfall_big_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Waterfall_drops_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Waterfall_drops_NE_System_2<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Waterfall_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Waterfall_small_splash_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Whistle_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Wind_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    Wind_Small_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    WindFaster_NE_System<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

# Module import path: /<EMAIL>/Pekao_Bank/GiveLikeSystem
(/<EMAIL>/Pekao_Bank:)GiveLikeSystem := module:
    AnimatingBackground<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    AnimatingMask<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    Concept<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    (/<EMAIL>/Pekao_Bank/GiveLikeSystem:)EnglishTexture<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    Heart<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    Heart_Pattern_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    JustHeart<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    JustLike<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    Like<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    Textes<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Hand_Painted_Starter_Kit
Hand_Painted_Starter_Kit := module:
    # Module import path: /<EMAIL>/Pekao_Bank/Hand_Painted_Starter_Kit/Texture
    (/<EMAIL>/Pekao_Bank/Hand_Painted_Starter_Kit:)Texture := module:
        Vol_17_2_Base_Color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        Vol_17_2_Height<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        Vol_17_2_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/KubaModele
(/<EMAIL>/Pekao_Bank:)KubaModele := module:
    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Arcade
    (/<EMAIL>/Pekao_Bank/KubaModele:)Arcade := module:
        GlassBridge_int_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/KubaModele/Arcade/GlassBridge_int_material:)tex<public>:texture = external {}

        GlassBridge_int<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Arcade_Play<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Arcade_tex_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/KubaModele/Arcade/M_Arcade_tex_material:)tex<public>:texture = external {}

        M_Arcade_tex<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Button_Animation_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

        M_Button_Animation<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_Button_Animation_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

        Mi_Button_Animation<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_Button_Animation_2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

        Mi_Button_Animation_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_Button_Animation_3_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

        Mi_Button_Animation_3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_gray_2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_gray_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_gray_3_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_gray_3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_gray_light_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_gray_light<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_gray_light_2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_gray_light_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_red_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_red<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_white_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_white<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Parkour_Inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/KubaModele/Arcade/Parkour_Inst_material:)tex<public>:texture = external {}

        Parkour_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        race_inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/KubaModele/Arcade/race_inst_material:)tex<public>:texture = external {}

        race_inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Arcade<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        tex_arcade_sample<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/KubaModele/Arcade:)tex_automat_glass_bridge<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        tex_automat_parkour<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/KubaModele/Arcade:)tex_automat_race<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        tex_play<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/ATM
    ATM := module:
        M_ATM_Screen<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_emission_white<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_ATM<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_ATM_Button_Down<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_ATM_Button_Up<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        tex_atm_pekao<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Barierki_custom_tex
    Barierki_custom_tex := module:
        M_Racetrack_Barrier_Custom_Tex<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        T_Racetrack_Barrier_Custom_Tex<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Racetrack_Barrier_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Racetrack_Barrier_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Crushing_Machine
    Crushing_Machine := module:
        Mi_Rock_1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Spec<public>:texture = external {}

            var Base<public>:texture = external {}

            var Normal<public>:texture = external {}

        Mi_Rock_1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_Rock_2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Spec<public>:texture = external {}

            var Base<public>:texture = external {}

            var Normal<public>:texture = external {}

        Mi_Rock_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_Rock_3_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Spec<public>:texture = external {}

            var Base<public>:texture = external {}

            var Normal<public>:texture = external {}

        Mi_Rock_3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        NE_Crushing_Machine<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        SM_Crushing_Machine<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Crushing_Machine_Gear<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Rock_1<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Rock_2<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Rock_3<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Rock_4<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Rock_5<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Rock_6<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Rock_7<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Rock_8<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        T_Apollo_RockPile_04_D<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Apollo_RockPile_04_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Apollo_RockPile_04_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_NaturalRock_AB_D<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_NaturalRock_AB_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_NaturalRock_AB_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_NaturalRock_C_D<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_NaturalRock_C_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_NaturalRock_C_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Cutting_Table
    Cutting_Table := module:
        CONSTRUCTION_MACHINE_Table_Saw_On_Load_01<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        M_Screen_OFF<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Screen_ON<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_Cutting_Table_Wood_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Spec<public>:texture = external {}

            var Base<public>:texture = external {}

            var Normal<public>:texture = external {}

        Mi_Cutting_Table_Wood<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_green_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_green<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        NS_Cutting_Table<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        SM_Cutting_Table<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Cutting_Table_Blade<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Cutting_Table_Wood_1<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Cutting_Table_Wood_2<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        WildWest_WoodPile_1_D<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        WildWest_WoodPile_1_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        WildWest_WoodPile_1_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Finish_Line
    Finish_Line := module:
        M_finish_line<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Finish_Line_Meta_SM_Finish_Line_Meta<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        tex_checker<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Flagi
    Flagi := module:
        # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Flagi/materials
        materials := module:
            M_Flag_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/KubaModele/Flagi/materials/M_Flag_material:)Texture<public>:texture = external {}

            M_Flag<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            M_Silver<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Flagi/meshes
        meshes := module:
            SM_Flag_PEKAO<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Flagi/tex
        tex := module:
            T_Flaga_PEKAO<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            tex_Litwa<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Tex_noise<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Laser
    (/<EMAIL>/Pekao_Bank/KubaModele:)Laser := module:
        M_beam<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_coal<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_emission_static<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Gem<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        NE_Laser<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        SM_Laser<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Laser_beam<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Laser_Coal<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Laser_Gem<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        T_Props_Metal_Coal_D<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Props_Metal_Coal_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Props_Metal_Coal_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Neon_Arcade
    Neon_Arcade := module:
        M_neon<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_neon_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Neon_Arcade<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Play_Stop_Button
    Play_Stop_Button := module:
        (/<EMAIL>/Pekao_Bank/KubaModele/Play_Stop_Button:)M_float_red_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        (/<EMAIL>/Pekao_Bank/KubaModele/Play_Stop_Button:)M_float_red<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Play_Button<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Stop_Button<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Ring
    Ring := module:
        SM_Ring<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Select_case_gold
    Select_case_gold := module:
        M_float_tex_case_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Specular<public>:float = external {}

            @editable
            var S_add<public>:float = external {}

            @editable
            var M_add<public>:float = external {}

            @editable
            var D_add<public>:float = external {}

            var D<public>:texture = external {}

            var S<public>:texture = external {}

            var M<public>:texture = external {}

            var N<public>:texture = external {}

        M_float_tex_case<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_float_tex_gold_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Specular<public>:float = external {}

            @editable
            var S_add<public>:float = external {}

            @editable
            var M_add<public>:float = external {}

            @editable
            var D_add<public>:float = external {}

            var D<public>:texture = external {}

            var S<public>:texture = external {}

            var M<public>:texture = external {}

            var N<public>:texture = external {}

        M_float_tex_gold<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Prop_GunGrate_05_M<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        Prop_GunGrate_05_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        Prop_GunGrate_05_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        SM_Select_case_gold<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        T_AD_Gold_Bar_D<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_AD_Gold_Bar_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_AD_Gold_Bar_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Select_gamepad
    Select_gamepad := module:
        (/<EMAIL>/Pekao_Bank/KubaModele/Select_gamepad:)M_float_red_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        (/<EMAIL>/Pekao_Bank/KubaModele/Select_gamepad:)M_float_red<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_float_white_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_float_white<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Select_gamepad<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Select_Island
    Select_Island := module:
        M_Challenge_Select_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var color1<public>:color = external {}

            @editable
            var color2<public>:color = external {}

        M_Challenge_Select<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Challenge_Select_Inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var color1<public>:color = external {}

            @editable
            var color2<public>:color = external {}

        M_Challenge_Select_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Challenge_Select_Inst_Inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var color1<public>:color = external {}

            @editable
            var color2<public>:color = external {}

        M_Challenge_Select_Inst_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_float_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_float<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_float_sand_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_float_sand<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_float_tex_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Specular<public>:float = external {}

            @editable
            var S_add<public>:float = external {}

            @editable
            var M_add<public>:float = external {}

            @editable
            var D_add<public>:float = external {}

            var D<public>:texture = external {}

            var S<public>:texture = external {}

            var M<public>:texture = external {}

            var N<public>:texture = external {}

        M_float_tex<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_float_water_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_float_water<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Select_Island<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        T_PalmTree_B_D<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_PalmTree_B_M<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_PalmTree_B_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_PalmTree_B_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Select_phone
    Select_phone := module:
        M_float_black_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_float_black<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_float_black_2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_float_black_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/KubaModele/Select_phone:)M_float_red_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        (/<EMAIL>/Pekao_Bank/KubaModele/Select_phone:)M_float_red<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Select_Phone<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Stadion
    Stadion := module:
        M_color_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metal<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Param<public>:color = external {}

        M_color<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_color_white_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metal<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Param<public>:color = external {}

        Mi_color_white<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Stadion_BANK<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Stadion_PEKAO<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Statue_0
    Statue_0 := module:
        M_tex_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metal<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/KubaModele/Statue_0/M_tex_material:)tex<public>:texture = external {}

        M_tex<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_tex_statue_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metal<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/KubaModele/Statue_0/M_tex_statue_material:)tex<public>:texture = external {}

        M_tex_statue<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_tex_statue_2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metal<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/KubaModele/Statue_0/M_tex_statue_2_material:)tex<public>:texture = external {}

        M_tex_statue_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_color_gold_statue_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metal<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Param<public>:color = external {}

        Mi_color_gold_statue<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Statue_0<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        T_AD_Marble_Floor_10_D_Beige<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_AD_Marble_Floor_10_D_Red<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_AD_Trim_01_D_Red<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Suitecase_money
    Suitecase_money := module:
        CashGroup01Blue_D<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        M_Suitcase_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Spec<public>:texture = external {}

            var Base<public>:texture = external {}

            var Normal<public>:texture = external {}

        M_Suitcase<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_tex_money_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metal<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/KubaModele/Suitecase_money/M_tex_money_material:)tex<public>:texture = external {}

        M_tex_money<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Prop_LargeSuitcases_01_D<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        Prop_LargeSuitcases_01_N<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        Prop_LargeSuitcases_01_S<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        SM_Suitcase_money<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/KubaModele/Teddy_bear
    Teddy_bear := module:
        M_solid_color_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_solid_color<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_teddy_bear_heart<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_black_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_black<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_brown_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_brown<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Mi_solid_color_gray_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Roughness<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Metallic<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Mi_solid_color_gray<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        sm_teddy_bear<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        tex_heart<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/KubaVFX
KubaVFX := module:
    # Module import path: /<EMAIL>/Pekao_Bank/KubaVFX/GlassBreakVFX
    GlassBreakVFX := module:
        M_Glass_VFX<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Glass_Broken_1<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Glass_Broken_10<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Glass_Broken_2<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Glass_Broken_3<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Glass_Broken_4<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Glass_Broken_5<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Glass_Broken_6<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Glass_Broken_7<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Glass_Broken_8<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Glass_Broken_9<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        VFX_Broken_Glass<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

# Module import path: /<EMAIL>/Pekao_Bank/L10N
L10N := module:
    # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl
    pl := module:
        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/GiveLikeSystem
        (/<EMAIL>/Pekao_Bank/L10N/pl:)GiveLikeSystem := module:
            (/<EMAIL>/Pekao_Bank/L10N/pl/GiveLikeSystem:)EnglishTexture<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/KubaModele
        (/<EMAIL>/Pekao_Bank/L10N/pl:)KubaModele := module:
            # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/KubaModele/Arcade
            (/<EMAIL>/Pekao_Bank/L10N/pl/KubaModele:)Arcade := module:
                (/<EMAIL>/Pekao_Bank/L10N/pl/KubaModele/Arcade:)tex_automat_glass_bridge<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/KubaModele/Arcade:)tex_automat_race<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Materials
        (/<EMAIL>/Pekao_Bank/L10N/pl:)Materials := module:
            # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Materials/ClaimInfo
            (/<EMAIL>/Pekao_Bank/L10N/pl/Materials:)ClaimInfo := module:
                (/<EMAIL>/Pekao_Bank/L10N/pl/Materials/ClaimInfo:)ClaimBank_Tex_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Modele
        (/<EMAIL>/Pekao_Bank/L10N/pl:)Modele := module:
            # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns
            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele:)OwnerSigns := module:
                # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji
                (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns:)NazwyLokacji := module:
                    # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury
                    (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji:)Textury := module:
                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)BRICK_HOUSE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)CAR_HEAVEN<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)CITY_BEACH<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)CITY_PARK<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)COLONY_HOUSE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)DURRR_BURGER<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)FORK_KNIVE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)GAS_STATION<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)ICE_CREAM_CART<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)ICE_CREAM_CENTER<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)LUXURY_HOUSE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)MARINA_HOTEL<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)MOVIES_FACTORY<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)PIZZA_PIT<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)SHINY_DIAMONDS<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)SNOW_CONES<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)SPACE_CONE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)SUPER_SPLASH_OBBY<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)TRAINING_CENTER<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)UNCLE_PETES_PIZZA<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)UNCLES_TOM_FARM<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes
                        (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury:)Incomes := module:
                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income1200<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income1500<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income200<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income250<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income3000<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income30000_eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income35<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income5<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income500<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                            (/<EMAIL>/Pekao_Bank/L10N/pl/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income800<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Tex
        (/<EMAIL>/Pekao_Bank/L10N/pl:)Tex := module:
            # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Tex/InteractionsImages
            (/<EMAIL>/Pekao_Bank/L10N/pl/Tex:)InteractionsImages := module:
                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/InteractionsImages:)boss<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/InteractionsImages:)ChooseBoss_ENG<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/InteractionsImages:)Delete<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/InteractionsImages:)HitToEarn_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/InteractionsImages:)plant<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/InteractionsImages:)tree<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons
            (/<EMAIL>/Pekao_Bank/L10N/pl/Tex:)TextOnButtons := module:
                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)BankingQuizEng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)banksquizeng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)BuyCosmetics_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)ChristmasQuiz_ENG<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)CybersecurityQuizEng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)GamingQuiz<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)InvestmentAcc_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)MathQuizEng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)PhoneMenuEng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)PlaytimeRewardsENG<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)PRKOUR_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)SaveandEarn_ENG<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)savingquizeng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/Tex/TextOnButtons:)SuperRace_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/VLocalization
        (/<EMAIL>/Pekao_Bank/L10N/pl:)VLocalization := module:
            # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/VLocalization/Assets
            (/<EMAIL>/Pekao_Bank/L10N/pl/VLocalization:)Assets := module:
                (/<EMAIL>/Pekao_Bank/L10N/pl/VLocalization/Assets:)T_WelcomeScreen<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/L10N/pl/VLocalization/Assets:)WB_LangSelect<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/VMinigames
        (/<EMAIL>/Pekao_Bank/L10N/pl:)VMinigames := module:
            (/<EMAIL>/Pekao_Bank/L10N/pl/VMinigames:)buildtime<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/VMinigames/Assets
            (/<EMAIL>/Pekao_Bank/L10N/pl/VMinigames:)Assets := module:
                (/<EMAIL>/Pekao_Bank/L10N/pl/VMinigames/Assets:)WBP_MinigameResult<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/VNotifications
        (/<EMAIL>/Pekao_Bank/L10N/pl:)VNotifications := module:
            (/<EMAIL>/Pekao_Bank/L10N/pl/VNotifications:)WBP_BigTopNotification<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/VPhone
        (/<EMAIL>/Pekao_Bank/L10N/pl:)VPhone := module:
            # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets
            (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone:)Assets := module:
                # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures
                (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets:)VTextures := module:
                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_Achievements<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_Deposit<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_Deposit2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_MainMenu<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_MainMenuFirst<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_PhoneQuestDone<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_Reg1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_Reg2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_Reg3<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/L10N/pl/VPhone/Assets/VTextures:)T_SavingsBg<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/L10N/pl/VTimeRewards
        (/<EMAIL>/Pekao_Bank/L10N/pl:)VTimeRewards := module:
            (/<EMAIL>/Pekao_Bank/L10N/pl/VTimeRewards:)T_TimeRewardBg<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Materials
(/<EMAIL>/Pekao_Bank:)Materials := module:
    graffititest<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    graffititest_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    PekoCoin<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    TransparentMaterial<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/Ads
    Ads := module:
        Admaterial<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SecondAd<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/BreathingRed
    BreathingRed := module:
        BreathingRedlight_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/ClaimInfo
    (/<EMAIL>/Pekao_Bank/Materials:)ClaimInfo := module:
        (/<EMAIL>/Pekao_Bank/Materials/ClaimInfo:)ClaimBank_Tex_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/Cloud
    Cloud := module:
        Cloudmaterial<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/Colours
    Colours := module:
        # Module import path: /<EMAIL>/Pekao_Bank/Materials/Colours/PlainColors
        PlainColors := module:
            Pink_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Color<public>:color = external {}

            (/<EMAIL>/Pekao_Bank/Materials/Colours/PlainColors:)Pink<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            PlainColorMaster_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Color<public>:color = external {}

            PlainColorMaster<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Materials/Colours/Uzzu_Colors_Pallete
        Uzzu_Colors_Pallete := module:
            _Colour_Mat_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Metalic<public>:float = external {}

                @editable
                var Specular<public>:float = external {}

                @editable
                var Roughness<public>:float = external {}

                @editable
                var Emission<public>:float = external {}

                @editable
                var Colour<public>:color = external {}

            _Colour_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Blue_4<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Brown_dark_4<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Brown_light<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Green_light_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Orange_7<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/Materials/Colours/Uzzu_Colors_Pallete:)Pink<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Red_dark<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Yellow_light_3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/FakeMirror
    FakeMirror := module:
        FakeMirror_mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/FX
    (/<EMAIL>/Pekao_Bank/Materials:)FX := module:
        Confetti_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/FX/Confetti_material:)Tex<public>:texture = external {}

        Confetti<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Materials/FX:)Explosion<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Piece<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Materials/FX:)Smoke<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Snow_material_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Radius<public>:float = external {}

            @editable
            var Hardness<public>:float = external {}

        Snow_material<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Waterfall_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Hardness<public>:float = external {}

            @editable
            var Time<public>:float = external {}

        Waterfall<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Watersplash_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Radius<public>:float = external {}

            @editable
            var Hardness<public>:float = external {}

        Watersplash<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Whitedot_sharp_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Radius<public>:float = external {}

            @editable
            var Hardness<public>:float = external {}

        Whitedot_sharp<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/Glass
    (/<EMAIL>/Pekao_Bank/Materials:)Glass := module:
        Glass_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Opacity<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        (/<EMAIL>/Pekao_Bank/Materials/Glass:)Glass<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/Landscape
    Landscape := module:
        MI_Creative_Landscape_Apollo_Standard_Inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var FN_DestructibeDamage_CrackPreview<public>:float = external {}

            @editable
            var FN_DestructibeDamage_CrackSize<public>:float = external {}

            @editable
            var FN_DestructibeDamage_CrackColorBrightness<public>:float = external {}

            @editable
            var WSDamageRadius<public>:float = external {}

            @editable
            var MaxDistSqr<public>:float = external {}

            @editable
            var Code_DestructionAmount<public>:float = external {}

            @editable
            var WSDamageRadiusAlt<public>:float = external {}

            @editable
            var DestructionMultiplier<public>:float = external {}

            @editable
            var CurieCharred<public>:float = external {}

            @editable
            var CurieSphericalMappingScale<public>:float = external {}

            @editable
            var CurieElementIndex<public>:float = external {}

            @editable
            var CurieLERP<public>:float = external {}

            @editable
            var Xscale<public>:float = external {}

            @editable
            var FlameGlowStrength_Low<public>:float = external {}

            @editable
            var CurieChared<public>:float = external {}

            @editable
            var BlendToIntermediate<public>:float = external {}

            @editable
            var EpicFlamesExponent<public>:float = external {}

            @editable
            var EpicFlamesScale<public>:float = external {}

            @editable
            var FlamesDivisorB<public>:float = external {}

            @editable
            var FlamesDivisorA<public>:float = external {}

            @editable
            var HeightFalloffRadius<public>:float = external {}

            @editable
            var HeightFalloffHardness<public>:float = external {}

            @editable
            var t45y34t3<public>:float = external {}

            @editable
            var LtoRBlendCont<public>:float = external {}

            @editable
            var DistanceNearEffect<public>:float = external {}

            @editable
            var DistanceFarEffect<public>:float = external {}

            @editable
            var GrsNearSpecMin<public>:float = external {}

            @editable
            var HD_Grass_Sub1<public>:float = external {}

            @editable
            var HD_Grass_Mod1<public>:float = external {}

            @editable
            var HD_Grass_Mod2<public>:float = external {}

            @editable
            var rw4twr42<public>:float = external {}

            @editable
            var HD_Grass_DebugAlteredHeight<public>:float = external {}

            @editable
            var g43t323zapo<public>:float = external {}

            @editable
            var RVT_MipDistanceNearEffect<public>:float = external {}

            @editable
            var RVT_MipDistanceFarEffect<public>:float = external {}

            @editable
            var RVT_MipDistanceMidEffect<public>:float = external {}

            @editable
            var HD_GrassGravelLayerCont<public>:float = external {}

            @editable
            var MudScale<public>:float = external {}

            @editable
            var HD_Gravel_Sub1<public>:float = external {}

            @editable
            var HD_Gravel_Mod1<public>:float = external {}

            @editable
            var HD_Gravel_Mod2<public>:float = external {}

            @editable
            var HD_Gravel_Add1<public>:float = external {}

            @editable
            var HD_Gravel_DebugAlteredHeight<public>:float = external {}

            @editable
            var NewSandNoiseLB<public>:float = external {}

            @editable
            var OceanLevel_VerticalOffset<public>:float = external {}

            @editable
            var MobileAsphaltSpec<public>:float = external {}

            @editable
            var MobileAsphaltRough<public>:float = external {}

            @editable
            var MobileAsphaltRough2<public>:float = external {}

            @editable
            var SizeCracks<public>:float = external {}

            @editable
            var AsphaltVariation<public>:float = external {}

            @editable
            var AsphaltMainScale<public>:float = external {}

            @editable
            var MudPuddleHeight<public>:float = external {}

            @editable
            var MudPuddleBlend<public>:float = external {}

            @editable
            var Roughness_MudLow<public>:float = external {}

            @editable
            var Roughness_MudHigh<public>:float = external {}

            @editable
            var Specular_MudLow<public>:float = external {}

            @editable
            var Specular_MudHigh<public>:float = external {}

            @editable
            var WSDeformationCenter<public>:color = external {}

            @editable
            var Code_PrecomputedHitVector<public>:color = external {}

            @editable
            var Code_PrecomputedHitVector_Prev_Frame<public>:color = external {}

            @editable
            var WSDeformationCenterAlt<public>:color = external {}

            @editable
            var EmissionColor<public>:color = external {}

            @editable
            var BlueprintColor<public>:color = external {}

            @editable
            var CuriePanSpeedTerrainWSv4<public>:color = external {}

            @editable
            var LargeRock_Scale<public>:color = external {}

            @editable
            var DicCliffLeakColor<public>:color = external {}

            @editable
            var MediumRock_Scale<public>:color = external {}

            @editable
            var LandscapeWorldUV<public>:color = external {}

            @editable
            var TestNewGrassColor<public>:color = external {}

            @editable
            var GrassMacroColorA<public>:color = external {}

            @editable
            var GrassMacroColorB<public>:color = external {}

            @editable
            var yellowish<public>:color = external {}

            @editable
            var TestNewColors<public>:color = external {}

            var BiomeTexture<public>:texture = external {}

            var Asphalt_SMR<public>:texture = external {}

        MI_Creative_Landscape_Apollo_Standard_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_FortniteLandscape_Customizable_01_Inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var FN_DestructibeDamage_CrackPreview<public>:float = external {}

            @editable
            var FN_DestructibeDamage_CrackSize<public>:float = external {}

            @editable
            var FN_DestructibeDamage_CrackColorBrightness<public>:float = external {}

            @editable
            var WSDamageRadius<public>:float = external {}

            @editable
            var MaxDistSqr<public>:float = external {}

            @editable
            var Code_DestructionAmount<public>:float = external {}

            @editable
            var WSDamageRadiusAlt<public>:float = external {}

            @editable
            var DestructionMultiplier<public>:float = external {}

            @editable
            var CurieCharred<public>:float = external {}

            @editable
            var CurieSphericalMappingScale<public>:float = external {}

            @editable
            var CurieElementIndex<public>:float = external {}

            @editable
            var CurieLERP<public>:float = external {}

            @editable
            var Xscale<public>:float = external {}

            @editable
            var FlameGlowStrength_Low<public>:float = external {}

            @editable
            var CurieChared<public>:float = external {}

            @editable
            var BlendToIntermediate<public>:float = external {}

            @editable
            var EpicFlamesExponent<public>:float = external {}

            @editable
            var EpicFlamesScale<public>:float = external {}

            @editable
            var FlamesDivisorB<public>:float = external {}

            @editable
            var FlamesDivisorA<public>:float = external {}

            @editable
            var HeightFalloffRadius<public>:float = external {}

            @editable
            var HeightFalloffHardness<public>:float = external {}

            @editable
            var Layer1_NormalMap_Intensity<public>:float = external {}

            @editable
            var Layer1_BaseColor_Heightmap_LayerBlendContrast<public>:float = external {}

            @editable
            var Layer1_Mobile_SpecularValue<public>:float = external {}

            @editable
            var Layer1_Mobile_RoughnessValue<public>:float = external {}

            @editable
            var Layer1_Mobile_MetallicValue<public>:float = external {}

            @editable
            var Layer2_TextureScale<public>:float = external {}

            @editable
            var Layer2_NormalMap_Intensity<public>:float = external {}

            @editable
            var Layer2_Mobile_SpecularValue<public>:float = external {}

            @editable
            var Layer2_Mobile_RoughnessValue<public>:float = external {}

            @editable
            var Layer2_Mobile_MetallicValue<public>:float = external {}

            @editable
            var Base_TextureScale<public>:float = external {}

            @editable
            var Base_NormalMap_Intensity<public>:float = external {}

            @editable
            var Base_Mobile_SpecularValue<public>:float = external {}

            @editable
            var Base_Mobile_RoughnessValue<public>:float = external {}

            @editable
            var Base_Mobile_MetallicValue<public>:float = external {}

            @editable
            var Cliff_ProjectionMask_TransitionContrast<public>:float = external {}

            @editable
            var Cliff_ProjectionMask_HeightmapTransitionContrast<public>:float = external {}

            @editable
            var Cliff_SlopeLayerMask_Power<public>:float = external {}

            @editable
            var Cliff_SlopeLayerMask_Multiply<public>:float = external {}

            @editable
            var Layer1_TextureScale<public>:float = external {}

            @editable
            var Layer1_BaseColor_Heightmap_Contribution<public>:float = external {}

            @editable
            var Layer2_BaseColor_Heightmap_Contribution<public>:float = external {}

            @editable
            var Layer2_BaseColor_Heightmap_LayerBlendContrast<public>:float = external {}

            @editable
            var Base_BaseColor_Heightmap_Contribution<public>:float = external {}

            @editable
            var Layer3_TextureScale<public>:float = external {}

            @editable
            var Layer3_NormalMap_Intensity<public>:float = external {}

            @editable
            var Layer3_Mobile_SpecularValue<public>:float = external {}

            @editable
            var Layer3_Mobile_RoughnessValue<public>:float = external {}

            @editable
            var Layer3_Mobile_MetallicValue<public>:float = external {}

            @editable
            var Layer3_BaseColor_Heightmap_Contribution<public>:float = external {}

            @editable
            var Layer3_BaseColor_Heightmap_LayerBlendContrast<public>:float = external {}

            @editable
            var Cliff_BaseColor_Heightmap_LayerBlendContrast<public>:float = external {}

            @editable
            var Cliff_NormalMap_Intensity<public>:float = external {}

            @editable
            var Cliff_Mobile_SpecularValue<public>:float = external {}

            @editable
            var Cliff_Mobile_RoughnessValue<public>:float = external {}

            @editable
            var Cliff_Mobile_MetallicValue<public>:float = external {}

            @editable
            var Cliff_BaseColor_Heightmap_Contribution<public>:float = external {}

            @editable
            var Layer4_TextureScale<public>:float = external {}

            @editable
            var Layer4_NormalMap_Intensity<public>:float = external {}

            @editable
            var Layer4_Mobile_SpecularValue<public>:float = external {}

            @editable
            var Layer4_Mobile_RoughnessValue<public>:float = external {}

            @editable
            var Layer4_Mobile_MetallicValue<public>:float = external {}

            @editable
            var Layer4_BaseColor_Heightmap_Contribution<public>:float = external {}

            @editable
            var Layer4_BaseColor_Heightmap_LayerBlendContrast<public>:float = external {}

            @editable
            var Layer5_TextureScale<public>:float = external {}

            @editable
            var Layer5_NormalMap_Intensity<public>:float = external {}

            @editable
            var Layer5_Mobile_SpecularValue<public>:float = external {}

            @editable
            var Layer5_Mobile_RoughnessValue<public>:float = external {}

            @editable
            var Layer5_Mobile_MetallicValue<public>:float = external {}

            @editable
            var Layer5_BaseColor_Heightmap_Contribution<public>:float = external {}

            @editable
            var Layer5_BaseColor_Heightmap_LayerBlendContrast<public>:float = external {}

            @editable
            var Layer6_TextureScale<public>:float = external {}

            @editable
            var Layer6_NormalMap_Intensity<public>:float = external {}

            @editable
            var Layer6_Mobile_SpecularValue<public>:float = external {}

            @editable
            var Layer6_Mobile_RoughnessValue<public>:float = external {}

            @editable
            var Layer6_Mobile_MetallicValue<public>:float = external {}

            @editable
            var Layer6_BaseColor_Heightmap_Contribution<public>:float = external {}

            @editable
            var Layer6_BaseColor_Heightmap_LayerBlendContrast<public>:float = external {}

            @editable
            var Layer7_HeightLerpContrast<public>:float = external {}

            @editable
            var Layer7_Roughness_Minimum<public>:float = external {}

            @editable
            var Layer7_Roughness_Maximum<public>:float = external {}

            @editable
            var Layer7_FlattenNormals<public>:float = external {}

            @editable
            var Layer7_Specular_Minimum<public>:float = external {}

            @editable
            var Layer7_Specular_Maximum<public>:float = external {}

            @editable
            var Layer7_Metallic_Minimum<public>:float = external {}

            @editable
            var Layer7_Metallic_Maximum<public>:float = external {}

            @editable
            var Base_NormalMap_FadeDistance<public>:float = external {}

            @editable
            var Base_BaseColorSlope_MaskPower<public>:float = external {}

            @editable
            var Base_BaseColorNoise_A_TextureScale<public>:float = external {}

            @editable
            var Base_BaseColorNoise_B_TextureScale<public>:float = external {}

            @editable
            var Base_BaseColorNoise_A_LowRange<public>:float = external {}

            @editable
            var Base_BaseColorNoise_A_HighRange<public>:float = external {}

            @editable
            var Base_BaseColorNoise_B_LowRange<public>:float = external {}

            @editable
            var Base_BaseColorNoise_B_HighRange<public>:float = external {}

            @editable
            var Base_BaseColorNoise_C_LowRange<public>:float = external {}

            @editable
            var Base_BaseColorNoise_C_HighRange<public>:float = external {}

            @editable
            var Base_BaseColorNoise_C_TextureScale<public>:float = external {}

            @editable
            var Base_BaseColorSlope_MaskMultiply<public>:float = external {}

            @editable
            var Base_NormalMap_FadeDistanceAmount<public>:float = external {}

            @editable
            var Base_BaseColorSlope_Amount<public>:float = external {}

            @editable
            var Base_UseGrassType<public>:float = external {}

            @editable
            var Layer7_Amount<public>:float = external {}

            @editable
            var Base_GrassType_Layer7<public>:float = external {}

            @editable
            var Layer7_HeightmapAsRoughness_Minimum<public>:float = external {}

            @editable
            var Layer7_HeightmapAsRoughness_Maximum<public>:float = external {}

            @editable
            var Layer7_UseHeightmapAsRoughness<public>:float = external {}

            @editable
            var Cliff_Metallic_Minimum<public>:float = external {}

            @editable
            var Cliff_Metallic_Maximum<public>:float = external {}

            @editable
            var Cliff_Roughness_Minimum<public>:float = external {}

            @editable
            var Cliff_Roughness_Maximum<public>:float = external {}

            @editable
            var WSDeformationCenter<public>:color = external {}

            @editable
            var Code_PrecomputedHitVector<public>:color = external {}

            @editable
            var Code_PrecomputedHitVector_Prev_Frame<public>:color = external {}

            @editable
            var WSDeformationCenterAlt<public>:color = external {}

            @editable
            var EmissionColor<public>:color = external {}

            @editable
            var BlueprintColor<public>:color = external {}

            @editable
            var CuriePanSpeedTerrainWSv4<public>:color = external {}

            @editable
            var Layer1_BaseColor_Tint<public>:color = external {}

            @editable
            var Layer2_BaseColor_Tint<public>:color = external {}

            @editable
            var Cliff_TextureScale<public>:color = external {}

            @editable
            var Base_BaseColorSlope_Tint<public>:color = external {}

            @editable
            var Cliff_ProjectionMask_BaseColorChannel<public>:color = external {}

            @editable
            var Layer1_BaseColor_Heightmap_Channel<public>:color = external {}

            @editable
            var Layer2_BaseColor_Heightmap_Channel<public>:color = external {}

            @editable
            var Base_BaseColor_Heightmap_Channel<public>:color = external {}

            @editable
            var Layer3_BaseColor_Tint<public>:color = external {}

            @editable
            var Layer3_BaseColor_Heightmap_Channel<public>:color = external {}

            @editable
            var Cliff_BaseColor_Tint<public>:color = external {}

            @editable
            var Cliff_BaseColor_Heightmap_Channel<public>:color = external {}

            @editable
            var Layer4_BaseColor_Tint<public>:color = external {}

            @editable
            var Layer4_BaseColor_Heightmap_Channel<public>:color = external {}

            @editable
            var Layer5_BaseColor_Tint<public>:color = external {}

            @editable
            var Layer5_BaseColor_Heightmap_Channel<public>:color = external {}

            @editable
            var Layer6_BaseColor_Tint<public>:color = external {}

            @editable
            var Layer6_BaseColor_Heightmap_Channel<public>:color = external {}

            @editable
            var Layer7_BaseColor_Tint2<public>:color = external {}

            @editable
            var Layer7_BaseColor_Tint1<public>:color = external {}

            @editable
            var Base_BaseColorNoise_Tint_Color1<public>:color = external {}

            @editable
            var Base_BaseColorNoise_Tint_Color2<public>:color = external {}

            @editable
            var Base_BaseColorNoise_Tint_Color3<public>:color = external {}

            @editable
            var Base_BaseColor_Tint<public>:color = external {}

            var Layer1_NormalMap_Texture<public>:texture = external {}

            var Layer1_SpecularRoughnessMetallic_Texture<public>:texture = external {}

            var Layer1_BaseColor_Texture<public>:texture = external {}

            var Layer2_NormalMap_Texture<public>:texture = external {}

            var Layer2_SpecularRoughnessMetallic_Texture<public>:texture = external {}

            var Layer2_BaseColor_Texture<public>:texture = external {}

            var Base_NormalMap_Texture<public>:texture = external {}

            var Base_SpecularRoughnessMetallic_Texture<public>:texture = external {}

            var Base_BaseColor_Texture<public>:texture = external {}

            var Cliff_NormalMap_Texture<public>:texture = external {}

            var Cliff_SpecularRoughnessMetallic_Texture<public>:texture = external {}

            var Cliff_BaseColor_Texture<public>:texture = external {}

            var Layer3_NormalMap_Texture<public>:texture = external {}

            var Layer3_SpecularRoughnessMetallic_Texture<public>:texture = external {}

            var Layer3_BaseColor_Texture<public>:texture = external {}

            var Layer4_NormalMap_Texture<public>:texture = external {}

            var Layer4_SpecularRoughnessMetallic_Texture<public>:texture = external {}

            var Layer4_BaseColor_Texture<public>:texture = external {}

            var Layer5_NormalMap_Texture<public>:texture = external {}

            var Layer5_SpecularRoughnessMetallic_Texture<public>:texture = external {}

            var Layer5_BaseColor_Texture<public>:texture = external {}

            var Layer6_NormalMap_Texture<public>:texture = external {}

            var Layer6_SpecularRoughnessMetallic_Texture<public>:texture = external {}

            var Layer6_BaseColor_Texture<public>:texture = external {}

            var Base_BaseColorNoise_Texture<public>:texture = external {}

        MI_FortniteLandscape_Customizable_01_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/Materials_w_mask
    Materials_w_mask := module:
        Baseball_glove_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        BlackDot_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_MaskedMaterial_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metallic<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var U_Coordinate<public>:float = external {}

            @editable
            var V_Coordinate<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_MaskedMaterial<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/Materials_w_Texture
    Materials_w_Texture := module:
        Basketball_ball_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Leafs_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_w_Texture_two_sided<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Material_Custom_Texture_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Metalic<public>:float = external {}

            @editable
            var Specular<public>:float = external {}

            @editable
            var Roughness<public>:float = external {}

            @editable
            var Color_boost<public>:float = external {}

        Material_Custom_Texture<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Material_w_opacity_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Hue<public>:float = external {}

        Material_w_opacity<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Measures_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Popcorn_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Whistle_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/natural
    natural := module:
        BronzeOreMaterial<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        GoldOreMat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        IronOreMat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer
    RotatingToPlayer := module:
        ChristmasQuiz_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/ChristmasQuiz_material:)Texture<public>:texture = external {}

        ChristmasQuiz<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        HitToEarnMoney_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/HitToEarnMoney_material:)Texture<public>:texture = external {}

        HitToEarnMoney<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst1_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst2_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst3_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst3_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst4_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst4_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst4<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst5_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst5_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst5<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst6_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst6_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst6<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst7_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst7_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst7<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst8_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst8_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst8<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        RotatingToPlayer_Mat_Inst9_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/RotatingToPlayer_Mat_Inst9_material:)Texture<public>:texture = external {}

        RotatingToPlayer_Mat_Inst9<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        TreesToCut_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Materials/RotatingToPlayer/TreesToCut_material:)Texture<public>:texture = external {}

        TreesToCut<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Materials/WorldBarrier
    WorldBarrier := module:
        Woldbarriermat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Meshes
(/<EMAIL>/Pekao_Bank:)Meshes := module:
    prezent_BaseColor<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    prezent_BaseColor_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    prezent_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    prezent_OcclusionRoughnessMetallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    prezentotwarty<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    prezentzamkniety<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Meshes/FX
    (/<EMAIL>/Pekao_Bank/Meshes:)FX := module:
        T_4Note_Single<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_8Note_double<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_8Note_single<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_confetti<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Meshes/Portal
    Portal := module:
        base<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        (/<EMAIL>/Pekao_Bank/Meshes/Portal:)texture_diffuse<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Meshes/Portal:)texture_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Meshes/Portal:)texture_metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Meshes/Portal:)texture_normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Meshes/Portal:)texture_pbr<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Meshes/Portal:)texture_roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Meshes/Strzalka
    Strzalka := module:
        (/<EMAIL>/Pekao_Bank/Meshes/Strzalka:)STRZALKA_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Meshes/Strzalka:)STRZALKA_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Meshes/Strzalka:)STRZALKA_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Meshes/Strzalka:)STRZALKA_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Meshes/Test
    (/<EMAIL>/Pekao_Bank/Meshes:)Test := module:
        # Module import path: /<EMAIL>/Pekao_Bank/Meshes/Test/arow
        arow := module:
            rotatingobj_mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            transparenttexture<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Modele
(/<EMAIL>/Pekao_Bank:)Modele := module:
    # Module import path: /<EMAIL>/Pekao_Bank/Modele/Button
    Button := module:
        PRZYCISK<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        PRZYCISK_001<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        PRZYCISK_002_Blue<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        PRZYCISK_002_GREEN<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        PRZYCISK_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        PRZYCISK_blue<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        PRZYCISK_GREEN<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        PRZYCISK_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        PRZYCISK_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Modele/Cosmetics
    Cosmetics := module:
        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Cosmetics/Bison
        Bison := module:
            bison<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            bison_mat_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var BaseColorTexture_Rotation<public>:float = external {}

                @editable
                var BaseColorTexture_TexCoord<public>:float = external {}

                @editable
                var MetallicRoughnessTexture_Rotation<public>:float = external {}

                @editable
                var MetallicRoughnessTexture_TexCoord<public>:float = external {}

                @editable
                var MetallicFactor<public>:float = external {}

                @editable
                var RoughnessFactor<public>:float = external {}

                @editable
                var NormalTexture_Rotation<public>:float = external {}

                @editable
                var NormalTexture_TexCoord<public>:float = external {}

                @editable
                var NormalScale<public>:float = external {}

                @editable
                var OcclusionTexture_Rotation<public>:float = external {}

                @editable
                var OcclusionTexture_TexCoord<public>:float = external {}

                @editable
                var OcclusionStrength<public>:float = external {}

                @editable
                var IOR<public>:float = external {}

                @editable
                var SpecularTexture_Rotation<public>:float = external {}

                @editable
                var SpecularTexture_TexCoord<public>:float = external {}

                @editable
                var SpecularFactor<public>:float = external {}

                @editable
                var EmissiveTexture_Rotation<public>:float = external {}

                @editable
                var EmissiveTexture_TexCoord<public>:float = external {}

                @editable
                var EmissiveStrength<public>:float = external {}

                @editable
                var AlphaCutoff<public>:float = external {}

                @editable
                var IridescenceIOR<public>:float = external {}

                @editable
                var IridescenceFactor<public>:float = external {}

                @editable
                var IridescenceThicknessMinimum<public>:float = external {}

                @editable
                var IridescenceThicknessMaximum<public>:float = external {}

                @editable
                var IridescenceTexture_Rotation<public>:float = external {}

                @editable
                var IridescenceTexture_TexCoord<public>:float = external {}

                @editable
                var IridescenceThicknessTexture_Rotation<public>:float = external {}

                @editable
                var IridescenceThicknessTexture_TexCoord<public>:float = external {}

                @editable
                var AnisotropyStrength<public>:float = external {}

                @editable
                var AnisotropyRotation<public>:float = external {}

                @editable
                var AnisotropyTexture_Rotation<public>:float = external {}

                @editable
                var AnisotropyTexture_TexCoord<public>:float = external {}

                @editable
                var BaseColorFactor<public>:color = external {}

                @editable
                var MetallicRoughnessTexture_OffsetScale<public>:color = external {}

                @editable
                var BaseColorTexture_OffsetScale<public>:color = external {}

                @editable
                var NormalTexture_OffsetScale<public>:color = external {}

                @editable
                var OcclusionTexture_OffsetScale<public>:color = external {}

                @editable
                var SpecularTexture_OffsetScale<public>:color = external {}

                @editable
                var EmissiveTexture_OffsetScale<public>:color = external {}

                @editable
                var EmissiveFactor<public>:color = external {}

                @editable
                var SpecularTexture_TilingMethod<public>:color = external {}

                @editable
                var OcclusionTexture_TilingMethod<public>:color = external {}

                @editable
                var NormalTexture_TilingMethod<public>:color = external {}

                @editable
                var EmissiveTexture_TilingMethod<public>:color = external {}

                @editable
                var MetallicRoughnessTexture_TilingMethod<public>:color = external {}

                @editable
                var BaseColorTexture_TilingMethod<public>:color = external {}

                @editable
                var IridescenceTexture_TilingMethod<public>:color = external {}

                @editable
                var IridescenceThicknessTexture_TilingMethod<public>:color = external {}

                @editable
                var IridescenceTexture_OffsetScale<public>:color = external {}

                @editable
                var IridescenceThicknessTexture_OffsetScale<public>:color = external {}

                @editable
                var AnisotropyTexture_OffsetScale<public>:color = external {}

                @editable
                var AnisotropyTexture_TilingMethod<public>:color = external {}

                var BaseColorTexture<public>:texture = external {}

                var MetallicRoughnessTexture<public>:texture = external {}

                var NormalTexture<public>:texture = external {}

                var OcclusionTexture<public>:texture = external {}

                var SpecularTexture<public>:texture = external {}

                var EmissiveTexture<public>:texture = external {}

                var IridescenceTexture<public>:texture = external {}

                var IridescenceThicknessTexture<public>:texture = external {}

                var AnisotropyTexture<public>:texture = external {}

            bison_mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            bison_texture_0<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Cosmetics/ChestPlate
        (/<EMAIL>/Pekao_Bank/Modele/Cosmetics:)ChestPlate := module:
            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/ChestPlate:)ChestPlate<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/ChestPlate:)texture_diffuse<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/ChestPlate:)texture_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/ChestPlate:)texture_metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/ChestPlate:)texture_normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/ChestPlate:)texture_pbr<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/ChestPlate:)texture_roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Cosmetics/MoneyBag
        (/<EMAIL>/Pekao_Bank/Modele/Cosmetics:)MoneyBag := module:
            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/MoneyBag:)MoneyBag<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/MoneyBag:)texture_diffuse<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/MoneyBag:)texture_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/MoneyBag:)texture_metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/MoneyBag:)texture_normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/MoneyBag:)texture_pbr<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/MoneyBag:)texture_roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Cosmetics/SantaHat
        (/<EMAIL>/Pekao_Bank/Modele/Cosmetics:)SantaHat := module:
            lambert2SG_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var DiffuseColorMapWeight<public>:float = external {}

                @editable
                var IOR<public>:float = external {}

                var DiffuseColorMap<public>:texture = external {}

            lambert2SG<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/SantaHat:)SantaHat<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            TEX_Texture_TopHat<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TEX_Texture_TopHat_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/SantaHat:)texture_diffuse<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/SantaHat:)texture_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/SantaHat:)texture_metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/SantaHat:)texture_normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/SantaHat:)texture_pbr<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/Modele/Cosmetics/SantaHat:)texture_roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TopHat<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Modele/Dropper
    (/<EMAIL>/Pekao_Bank/Modele:)Dropper := module:
        DROPPER<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        DROPPER_1A<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        DROPPER_1B<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        DROPPER_2A<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        DROPPER_2B<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        DROPPER_3A<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        DROPPER_3B<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        DROPPER_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        DROPPER_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        DROPPER_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        DROPPER_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        TASMA<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        TASMA1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        TASMA_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        TASMA_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        TASMA_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        TASMA_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Modele/Logotypy
    Logotypy := module:
        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Logotypy/Baner
        Baner := module:
            BANER<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            BANER_LOGO_GENERALI_BaseColor<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            BANER_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            BANER_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            BANER_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            LOGO_GENERALI<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Logotypy/Coin
        (/<EMAIL>/Pekao_Bank/Modele/Logotypy:)Coin := module:
            (/<EMAIL>/Pekao_Bank/Modele/Logotypy/Coin:)COIN<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            COIN1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            COIN_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            COIN_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            COIN_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            COIN_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Logotypy/Flaga
        Flaga := module:
            FLAGA<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            FLAGA_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            FLAGA_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Material_011<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Material_011_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Logotypy/Logo
        Logo := module:
            LOGO<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            LOGO1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            LOGO_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            LOGO_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            LOGO_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            LOGO_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Logotypy/Zubr
        Zubr := module:
            NeonRed<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            ZUBR<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            ZUBR1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            ZUBR_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            ZUBR_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            ZUBR_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            ZUBR_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Modele/Low_Poly_Town
    Low_Poly_Town := module:
        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Low_Poly_Town/Materials
        (/<EMAIL>/Pekao_Bank/Modele/Low_Poly_Town:)Materials := module:
            M_Atlas<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Low_Poly_Town/Models
        Models := module:
            # Module import path: /<EMAIL>/Pekao_Bank/Modele/Low_Poly_Town/Models/Road
            Road := module:
                SM_Hydrant<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_1<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_2<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_3<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_4<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_5<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_6<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_7<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_8<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_Barrier<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Road_Gas_Station<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_SignBoard_1<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_SignBoard_2<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_SignBoard_3<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_SignBoard_4<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_SignBoard_5<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Street_Board_1<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Street_Board_2<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Street_Board_3<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Street_Light_1<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Street_Light_2<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Street_Well_1<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Street_Well_2<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Traffic_Light_1<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Traffic_Light_2<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Traffic_Light_3A<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Traffic_Light_3B<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

                SM_Traffic_Light_3C<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/Low_Poly_Town/Textures
        (/<EMAIL>/Pekao_Bank/Modele/Low_Poly_Town:)Textures := module:
            T_Atlas<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Modele/OwnerSigns
    (/<EMAIL>/Pekao_Bank/Modele:)OwnerSigns := module:
        BG<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        BG_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        BG_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        BG_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        BusnessNameAndIncome_MAt_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/BusnessNameAndIncome_MAt_material:)Texture<public>:texture = external {}

            var IncomeInfo<public>:texture = external {}

        BusnessNameAndIncome_MAt<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        FORSALE<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        FORSALE_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        FORSALE_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        FORSALE_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        FORSALE_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        NAPIS_ALPHA<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        OwnerSign<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        PlayerColor1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        PlayerColor2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        PlayerColor3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        PlayerColorNone<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji
        (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns:)NazwyLokacji := module:
            BRICKHOUSE_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/BRICKHOUSE_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            BRICKHOUSE_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            CARHEAVEN_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/CARHEAVEN_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            CARHEAVEN_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            CITYBEACH_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/CITYBEACH_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            CITYBEACH_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            CITYPARK_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/CITYPARK_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            CITYPARK_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            COLONYHOUSE_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/COLONYHOUSE_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            COLONYHOUSE_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            DURRRBURGER_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/DURRRBURGER_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            DURRRBURGER_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            FORKKNIVE_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/FORKKNIVE_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            FORKKNIVE_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            GASSTATION_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/GASSTATION_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            GASSTATION_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            ICECREAMCART_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/ICECREAMCART_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            ICECREAMCART_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            ICECREAMCENTER_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/ICECREAMCENTER_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            ICECREAMCENTER_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            LUXURYHOUSE_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/LUXURYHOUSE_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            LUXURYHOUSE_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MARINAHOTEL_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/MARINAHOTEL_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            MARINAHOTEL_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MOVIESFACTORY_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/MOVIESFACTORY_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            MOVIESFACTORY_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            PIZZAPIT_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/PIZZAPIT_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            PIZZAPIT_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            SHINYDIAMONDS_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/SHINYDIAMONDS_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            SHINYDIAMONDS_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            SnowCones_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/SnowCones_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            SnowCones_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            SPACECONE_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/SPACECONE_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            SPACECONE_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            SUPERSPLASHOBBY_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/SUPERSPLASHOBBY_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            SUPERSPLASHOBBY_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            TRAININGCENTER_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/TRAININGCENTER_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            TRAININGCENTER_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            UNCLEPETESPIZZA_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/UNCLEPETESPIZZA_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            UNCLEPETESPIZZA_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            UNCLESTOMFARM_INST_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/UNCLESTOMFARM_INST_material:)Texture<public>:texture = external {}

                var IncomeInfo<public>:texture = external {}

            UNCLESTOMFARM_INST<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            # Module import path: /<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury
            (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji:)Textury := module:
                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)BRICK_HOUSE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)CAR_HEAVEN<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)CITY_BEACH<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)CITY_PARK<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)COLONY_HOUSE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)DURRR_BURGER<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)FORK_KNIVE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)GAS_STATION<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)ICE_CREAM_CART<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)ICE_CREAM_CENTER<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)LUXURY_HOUSE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)MARINA_HOTEL<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)MOVIES_FACTORY<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)PIZZA_PIT<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)SHINY_DIAMONDS<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)SNOW_CONES<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)SPACE_CONE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)SUPER_SPLASH_OBBY<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)TRAINING_CENTER<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)UNCLE_PETES_PIZZA<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)UNCLES_TOM_FARM<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                # Module import path: /<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes
                (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury:)Incomes := module:
                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income1200<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income1500<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income200<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income250<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income3000<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income30000_eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income35<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income5<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income500<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

                    (/<EMAIL>/Pekao_Bank/Modele/OwnerSigns/NazwyLokacji/Textury/Incomes:)Income800<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Modele/Piggy
    Piggy := module:
        base_basic_pbr<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        base_basic_pbr_texture_0<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        base_basic_pbr_texture_1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        base_basic_pbr_texture_2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        Blow_up_Piggy<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        M_CashCoin<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_CashCoin_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_CashCoinn<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        NewMaterial<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        place_holder_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var BaseColorTexture_Rotation<public>:float = external {}

            @editable
            var BaseColorTexture_TexCoord<public>:float = external {}

            @editable
            var MetallicRoughnessTexture_Rotation<public>:float = external {}

            @editable
            var MetallicRoughnessTexture_TexCoord<public>:float = external {}

            @editable
            var MetallicFactor<public>:float = external {}

            @editable
            var RoughnessFactor<public>:float = external {}

            @editable
            var NormalTexture_Rotation<public>:float = external {}

            @editable
            var NormalTexture_TexCoord<public>:float = external {}

            @editable
            var NormalScale<public>:float = external {}

            @editable
            var OcclusionTexture_Rotation<public>:float = external {}

            @editable
            var OcclusionTexture_TexCoord<public>:float = external {}

            @editable
            var OcclusionStrength<public>:float = external {}

            @editable
            var IOR<public>:float = external {}

            @editable
            var SpecularTexture_Rotation<public>:float = external {}

            @editable
            var SpecularTexture_TexCoord<public>:float = external {}

            @editable
            var SpecularFactor<public>:float = external {}

            @editable
            var EmissiveTexture_Rotation<public>:float = external {}

            @editable
            var EmissiveTexture_TexCoord<public>:float = external {}

            @editable
            var EmissiveStrength<public>:float = external {}

            @editable
            var AlphaCutoff<public>:float = external {}

            @editable
            var IridescenceIOR<public>:float = external {}

            @editable
            var IridescenceFactor<public>:float = external {}

            @editable
            var IridescenceThicknessMinimum<public>:float = external {}

            @editable
            var IridescenceThicknessMaximum<public>:float = external {}

            @editable
            var IridescenceTexture_Rotation<public>:float = external {}

            @editable
            var IridescenceTexture_TexCoord<public>:float = external {}

            @editable
            var IridescenceThicknessTexture_Rotation<public>:float = external {}

            @editable
            var IridescenceThicknessTexture_TexCoord<public>:float = external {}

            @editable
            var AnisotropyStrength<public>:float = external {}

            @editable
            var AnisotropyRotation<public>:float = external {}

            @editable
            var AnisotropyTexture_Rotation<public>:float = external {}

            @editable
            var AnisotropyTexture_TexCoord<public>:float = external {}

            @editable
            var BaseColorFactor<public>:color = external {}

            @editable
            var MetallicRoughnessTexture_OffsetScale<public>:color = external {}

            @editable
            var BaseColorTexture_OffsetScale<public>:color = external {}

            @editable
            var NormalTexture_OffsetScale<public>:color = external {}

            @editable
            var OcclusionTexture_OffsetScale<public>:color = external {}

            @editable
            var SpecularTexture_OffsetScale<public>:color = external {}

            @editable
            var EmissiveTexture_OffsetScale<public>:color = external {}

            @editable
            var EmissiveFactor<public>:color = external {}

            @editable
            var SpecularTexture_TilingMethod<public>:color = external {}

            @editable
            var OcclusionTexture_TilingMethod<public>:color = external {}

            @editable
            var NormalTexture_TilingMethod<public>:color = external {}

            @editable
            var EmissiveTexture_TilingMethod<public>:color = external {}

            @editable
            var MetallicRoughnessTexture_TilingMethod<public>:color = external {}

            @editable
            var BaseColorTexture_TilingMethod<public>:color = external {}

            @editable
            var IridescenceTexture_TilingMethod<public>:color = external {}

            @editable
            var IridescenceThicknessTexture_TilingMethod<public>:color = external {}

            @editable
            var IridescenceTexture_OffsetScale<public>:color = external {}

            @editable
            var IridescenceThicknessTexture_OffsetScale<public>:color = external {}

            @editable
            var AnisotropyTexture_OffsetScale<public>:color = external {}

            @editable
            var AnisotropyTexture_TilingMethod<public>:color = external {}

            var BaseColorTexture<public>:texture = external {}

            var MetallicRoughnessTexture<public>:texture = external {}

            var NormalTexture<public>:texture = external {}

            var OcclusionTexture<public>:texture = external {}

            var SpecularTexture<public>:texture = external {}

            var EmissiveTexture<public>:texture = external {}

            var IridescenceTexture<public>:texture = external {}

            var IridescenceThicknessTexture<public>:texture = external {}

            var AnisotropyTexture<public>:texture = external {}

        place_holder<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        texture_diffuse_gold<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Modele/RotatingArrow
    RotatingArrow := module:
        STRZALKA<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        (/<EMAIL>/Pekao_Bank/Modele/RotatingArrow:)STRZALKA_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        STRZALKA_Base_color_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Modele/RotatingArrow:)STRZALKA_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Modele/RotatingArrow:)STRZALKA_Normal<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Modele/RotatingArrow:)STRZALKA_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Modele/Screen
    Screen := module:
        DISPLAY<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        DISPLAY_MASK<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        EKRAN<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        EKRAN1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        EKRAN_Base_color<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        EKRAN_Metallic<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        EKRAN_Mixed_AO<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        EKRAN_Roughness<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/RankVFX
RankVFX := module:
    Mat_rank_0_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
        var RankTexture<public>:texture = external {}

    Mat_rank_0<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    Mesh_rank_0<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    SantaHatPresent<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    VFX_rank_0<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    VFX_rank_2<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Sequences
Sequences := module:
    FOLEY_STEP_Wood_Step_05<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

    Male_Voice_Good_Job_Soldier<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

    Metal_Crash<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

    MoneyBagDrop_BW_9349<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

    Well_done<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Sequences/Droppers
    Droppers := module:
        # Module import path: /<EMAIL>/Pekao_Bank/Sequences/Droppers/Laser
        (/<EMAIL>/Pekao_Bank/Sequences/Droppers:)Laser := module:
            # Module import path: /<EMAIL>/Pekao_Bank/Sequences/Droppers/Laser/Fail
            Fail := module:
                MachineStart_MET02_25_1<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Sequences/HeliStart
    HeliStart := module:
        Air_Traffic_Control_Tower_1<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Go_Hard_nointo<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Go_Hard_vocal_49746232<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Sounds
Sounds := module:
    # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Dropper
    (/<EMAIL>/Pekao_Bank/Sounds:)Dropper := module:
        Large_Conveyor_Belt_Operation_Loop_10<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        (/<EMAIL>/Pekao_Bank/Sounds/Dropper:)Money_01<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        (/<EMAIL>/Pekao_Bank/Sounds/Dropper:)Money_02<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Toy_robot<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Music
    Music := module:
        Nocturne<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Music/ChildMusic
        ChildMusic := module:
            Full_Track<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            fun___happy_kids<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Happy<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Music/GlassBridgeMusic
        GlassBridgeMusic := module:
            Cartoon_Character__Carousel<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Happy_Circus<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            ThatIsFunny<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Music/HipHopRadio
        HipHopRadio := module:
            Full_V<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Trendy_Hip_Hop<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Music/HotelMusic
        HotelMusic := module:
            Hotel_Lounge<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Lounge_Hotel<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Luxury_Lounge_Hotel<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Music/LoFiradio
        LoFiradio := module:
            Lofi_Chill<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Music/MusicInArcade
        MusicInArcade := module:
            Action_Energy_Fashion_Indie_Rock_by_Andy_Slatter<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Arcade_Game_Room_Ambience<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            LOOP<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Music/MusicInBank
        MusicInBank := module:
            ASX_RED_41621489_The_Phoenix_inst<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Beyond_The_Sun_inst_46180366<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Good_Vlog<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Keep_It_Moving_inst_105954684<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Living_It_Up_inst_110110300<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Lost_Without_You_inst_54627932<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            No_One_inst_54627580<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Sky_Is_The_Limit_vocal_111830068<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/Music/RaceMusic
        RaceMusic := module:
            ASX_RED_40536087_Work_inst<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Sounds/SFX
    SFX := module:
        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/SFX/Beach
        Beach := module:
            Sandy_Beach_Waves_Loop<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/SFX/GlassBreak
        GlassBreak := module:
            Glass_Break01<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Glass_Break02<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Glass_Break03<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Glass_Break04<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/SFX/Glasshit
        Glasshit := module:
            IMPACT_GLASS_Window_Punch_Deep_02<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            IMPACT_GLASS_Window_Punch_Deep_03<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/SFX/OpenSea
        OpenSea := module:
            open_sea_ambience_bali_loop<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/SFX/Pianio
        Pianio := module:
            do2<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            fa<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            la<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            mi<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            re<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            re2<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            so<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/SFX/UI
        (/<EMAIL>/Pekao_Bank/Sounds/SFX:)UI := module:
            Building_Upgrade_1<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Building_Upgrade_2<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Building_Upgrade_3<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Cartoon_Upgrade_1<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Cartoon_Upgrade_2<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Extra_Reward_1<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Extra_Reward_2<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Extra_Reward_3<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Level_Completed<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            (/<EMAIL>/Pekao_Bank/Sounds/SFX/UI:)Money_01<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            (/<EMAIL>/Pekao_Bank/Sounds/SFX/UI:)Money_02<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Save_03<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Save_Game_Button<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Wrong_Answer<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/Sounds/SFX/WaterSplashes
        WaterSplashes := module:
            Splash_01<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Splash_02<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            Splash_03<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

            VFX_WaterSound<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

# Module import path: /<EMAIL>/Pekao_Bank/StoreGiftPacks
StoreGiftPacks := module:
    # Module import path: /<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets
    TwoDAssets := module:
        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Amethyst_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Amethyst_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Amethyst_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Amethyst_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Amethyst_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Amethyst_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Copper_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Copper_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Copper_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Copper_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Copper_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Copper_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Crystal_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Crystal_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Crystal_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Crystal_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Crystal_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Crystal_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Diamond_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Diamond_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Diamond_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Diamond_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Diamond_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Diamond_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Emerald_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Emerald_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Emerald_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Emerald_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Emerald_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Emerald_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Gold_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Gold_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Gold_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Gold_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Gold_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Gold_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Jade_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Jade_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Jade_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Jade_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Jade_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Jade_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Pearl_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Pearl_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Pearl_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Pearl_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Pearl_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Pearl_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Silver_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Silver_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Silver_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Silver_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Silver_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets:)Silver_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG
        PNG := module:
            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Amethyst_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Amethyst_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Amethyst_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Amethyst_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Amethyst_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Amethyst_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Copper_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Copper_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Copper_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Copper_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Copper_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Copper_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Crystal_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Crystal_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Crystal_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Crystal_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Crystal_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Crystal_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Diamond_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Diamond_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Diamond_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Diamond_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Diamond_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Diamond_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Emerald_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Emerald_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Emerald_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Emerald_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Emerald_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Emerald_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Gold_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Gold_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Gold_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Gold_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Gold_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Gold_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Jade_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Jade_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Jade_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Jade_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Jade_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Jade_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Pearl_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Pearl_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Pearl_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Pearl_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Pearl_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Pearl_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Silver_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Silver_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Silver_03<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Silver_04<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Silver_05<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/StoreGiftPacks/TwoDAssets/PNG:)Silver_06<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Test
(/<EMAIL>/Pekao_Bank:)Test := module:
    Test_mat_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
        @editable
        var BlendToIntermediate<public>:float = external {}

        @editable
        var CostMetalNumber<public>:float = external {}

        @editable
        var HeightScale<public>:float = external {}

        @editable
        var ScreenBrightness<public>:float = external {}

        @editable
        var Unaffordable<public>:float = external {}

        @editable
        var (/<EMAIL>/Pekao_Bank/Test/Test_mat_material:)Fail<public>:float = external {}

        @editable
        var Success<public>:float = external {}

        @editable
        var Slide<public>:float = external {}

        @editable
        var Distort<public>:float = external {}

        @editable
        var TimerStartTime<public>:float = external {}

        @editable
        var TimerDuration<public>:float = external {}

        @editable
        var CostWoodNumber<public>:float = external {}

        @editable
        var CostStoneNumber<public>:float = external {}

        @editable
        var NumItems<public>:float = external {}

        @editable
        var BlueprintColor<public>:color = external {}

        @editable
        var ScreenColor<public>:color = external {}

        @editable
        var TextColor<public>:color = external {}

        @editable
        var TextColorFail<public>:color = external {}

        @editable
        var ScreenColorFail<public>:color = external {}

        @editable
        var ItemSelectionVector<public>:color = external {}

        var WeaponIcon<public>:texture = external {}

        var ResourceIcon<public>:texture = external {}

        var ResourceIconMetal<public>:texture = external {}

        var ResourceIconStone<public>:texture = external {}

    Test_mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Tex
(/<EMAIL>/Pekao_Bank:)Tex := module:
    fakeislad<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    fakeislad_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    Pekao_flag<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    pekao_pion_cmyk<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    pekao_pion_cmyk_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    PekoLogoPoz<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    PekoLogoPoz_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    powrotdopunktukontrolnego<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    resetpostepu<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Tex/InteractionsImages
    (/<EMAIL>/Pekao_Bank/Tex:)InteractionsImages := module:
        _5000_<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        _5000__Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Tex/InteractionsImages:)boss<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        boss_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Tex/InteractionsImages:)ChooseBoss_ENG<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        ChooseBoss_ENG_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Tex/InteractionsImages:)Delete<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        Delete_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        (/<EMAIL>/Pekao_Bank/Tex/InteractionsImages:)HitToEarn_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/InteractionsImages:)plant<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        plant_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        PlayToEarn_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/InteractionsImages:)tree<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Tex/Portals
    Portals := module:
        ScreenShot00086<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        ScreenShot00086_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        ScreenShot00087<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        ScreenShot00087_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        ScreenShot00088<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        ScreenShot00088_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Tex/Posters
    Posters := module:
        poster<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        poster1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        poster2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Tex/TextOnButtons
    (/<EMAIL>/Pekao_Bank/Tex:)TextOnButtons := module:
        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)BankingQuizEng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)banksquizeng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)BuyCosmetics_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)ChristmasQuiz_ENG<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)CybersecurityQuizEng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)GamingQuiz<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)InvestmentAcc_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)MathQuizEng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        pekaoquiz<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)PhoneMenuEng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)PlaytimeRewardsENG<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)PRKOUR_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)SaveandEarn_ENG<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)savingquizeng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/Tex/TextOnButtons:)SuperRace_Eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/TycoonPack
TycoonPack := module:
    # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Material
    Material := module:
        Arrow_Graphic__1__Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        buy_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        claim_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Creative_Coin_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        HammerBlack_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Param<public>:color = external {}

        HammerBlack<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        HammerBrown_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Param<public>:color = external {}

        HammerBrown<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        HammerGrey_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Param<public>:color = external {}

        HammerGrey<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        HammerSilver_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Param<public>:color = external {}

        HammerSilver<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon10_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon10<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon11_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon11<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon12_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon12<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon13_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon13<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon14_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon14<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon15_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon15<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon3_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon4_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon4<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon5_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon5<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon6_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon6<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon7_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon7<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon8_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon8<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Icon9_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        Icon9<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Inst_Popup_Arrow_01a_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Sharpness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Inst_Popup_Arrow_01a<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Inst_Popup_Arrow_01b_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Sharpness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Inst_Popup_Arrow_01b<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Inst_Popup_Smoke_01_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Sharpness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        Inst_Popup_Smoke_01<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        LEVELUP_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_100b_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_200_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_500b_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_50_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_BubbleCell_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Light_X<public>:float = external {}

            @editable
            var Light_Y<public>:float = external {}

            @editable
            var Light_Z<public>:float = external {}

            @editable
            var Strenght<public>:float = external {}

            @editable
            var Strenght_1<public>:float = external {}

            @editable
            var ShadowColor<public>:color = external {}

            @editable
            var LitColor<public>:color = external {}

            @editable
            var BlueprintLightning<public>:color = external {}

        M_BubbleCell<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Cloud<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Icon_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            var Param<public>:texture = external {}

        M_Icon<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Master_Mask_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

            var T_Poster_Mask<public>:texture = external {}

        M_Master_Mask<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Money<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Popup_Arrow_01_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Sharpness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_Popup_Arrow_01<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_Popup_Smoke_01_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Sharpness<public>:float = external {}

            @editable
            var Color<public>:color = external {}

        M_Popup_Smoke_01<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Aura_1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Deormation_Strength<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_X<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_Y<public>:float = external {}

            @editable
            var Erosion_Strength<public>:float = external {}

            @editable
            var Erosion_Contrast<public>:float = external {}

            @editable
            var Deformation_Noise_Tile_U<public>:float = external {}

            @editable
            var Deformation_Noise_Tile_V<public>:float = external {}

            @editable
            var Deformation_Noise_Speed_X<public>:float = external {}

            @editable
            var Deformation_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_y<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Fade_Distance<public>:float = external {}

            @editable
            var Outer_Mask_Length<public>:float = external {}

            @editable
            var Test_Color<public>:color = external {}

            var Main_Noise_Tex<public>:texture = external {}

            var Erosion_Noise<public>:texture = external {}

            var Param<public>:texture = external {}

        MI_Aura_1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Aura_Gradient_1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Deormation_Strength<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_X<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_Y<public>:float = external {}

            @editable
            var Erosion_Strength<public>:float = external {}

            @editable
            var Erosion_Contrast<public>:float = external {}

            @editable
            var Deformation_Noise_Tile_U<public>:float = external {}

            @editable
            var Deformation_Noise_Tile_V<public>:float = external {}

            @editable
            var Deformation_Noise_Speed_X<public>:float = external {}

            @editable
            var Deformation_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_y<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Fade_Distance<public>:float = external {}

            @editable
            var Outer_Mask_Length<public>:float = external {}

            @editable
            var Test_Color<public>:color = external {}

            var Main_Noise_Tex<public>:texture = external {}

            var Erosion_Noise<public>:texture = external {}

            var Param<public>:texture = external {}

        MI_Aura_Gradient_1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_BubbleCellParticle_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Light_X<public>:float = external {}

            @editable
            var Light_Y<public>:float = external {}

            @editable
            var Light_Z<public>:float = external {}

            @editable
            var Strenght<public>:float = external {}

            @editable
            var Strenght_1<public>:float = external {}

            @editable
            var ShadowColor<public>:color = external {}

            @editable
            var LitColor<public>:color = external {}

            @editable
            var BlueprintLightning<public>:color = external {}

        MI_BubbleCellParticle<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Exclamation_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

            var T_Poster_Mask<public>:texture = external {}

        MI_Exclamation<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Feeling_Bad_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

            var T_Poster_Mask<public>:texture = external {}

        MI_Feeling_Bad<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Feeling_Good_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

            var T_Poster_Mask<public>:texture = external {}

        MI_Feeling_Good<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Feeling_Sulky_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

            var T_Poster_Mask<public>:texture = external {}

        MI_Feeling_Sulky<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Interogation_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

            var T_Poster_Mask<public>:texture = external {}

        MI_Interogation<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Object_1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Fresnel_Ex<public>:float = external {}

            @editable
            var Fresnel_Reflect<public>:float = external {}

            @editable
            var Noise_Strength<public>:float = external {}

            @editable
            var Fresnel_Opacity<public>:float = external {}

            @editable
            var Noise_Contrast<public>:float = external {}

            @editable
            var Deform_Noise_Speed_X<public>:float = external {}

            @editable
            var Deform_Noise_Speed_Y<public>:float = external {}

            @editable
            var Deform_Noise_Tile_U<public>:float = external {}

            @editable
            var Deform_Noise_Tile_V<public>:float = external {}

            @editable
            var Deform_Noise_Strength<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Colot_Test<public>:color = external {}

            var Noise_Tex<public>:texture = external {}

            var Noise_Tex_2<public>:texture = external {}

            var Deform_Noise_Tex<public>:texture = external {}

        MI_Object_1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Object_1_Inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Fresnel_Ex<public>:float = external {}

            @editable
            var Fresnel_Reflect<public>:float = external {}

            @editable
            var Noise_Strength<public>:float = external {}

            @editable
            var Fresnel_Opacity<public>:float = external {}

            @editable
            var Noise_Contrast<public>:float = external {}

            @editable
            var Deform_Noise_Speed_X<public>:float = external {}

            @editable
            var Deform_Noise_Speed_Y<public>:float = external {}

            @editable
            var Deform_Noise_Tile_U<public>:float = external {}

            @editable
            var Deform_Noise_Tile_V<public>:float = external {}

            @editable
            var Deform_Noise_Strength<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Colot_Test<public>:color = external {}

            var Noise_Tex<public>:texture = external {}

            var Noise_Tex_2<public>:texture = external {}

            var Deform_Noise_Tex<public>:texture = external {}

        MI_Object_1_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Object_1_Inst1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Fresnel_Ex<public>:float = external {}

            @editable
            var Fresnel_Reflect<public>:float = external {}

            @editable
            var Noise_Strength<public>:float = external {}

            @editable
            var Fresnel_Opacity<public>:float = external {}

            @editable
            var Noise_Contrast<public>:float = external {}

            @editable
            var Deform_Noise_Speed_X<public>:float = external {}

            @editable
            var Deform_Noise_Speed_Y<public>:float = external {}

            @editable
            var Deform_Noise_Tile_U<public>:float = external {}

            @editable
            var Deform_Noise_Tile_V<public>:float = external {}

            @editable
            var Deform_Noise_Strength<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Colot_Test<public>:color = external {}

            var Noise_Tex<public>:texture = external {}

            var Noise_Tex_2<public>:texture = external {}

            var Deform_Noise_Tex<public>:texture = external {}

        MI_Object_1_Inst1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Object_2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Fresnel_Ex<public>:float = external {}

            @editable
            var Fresnel_Reflect<public>:float = external {}

            @editable
            var Noise_Strength<public>:float = external {}

            @editable
            var Fresnel_Opacity<public>:float = external {}

            @editable
            var Noise_Contrast<public>:float = external {}

            @editable
            var Deform_Noise_Speed_X<public>:float = external {}

            @editable
            var Deform_Noise_Speed_Y<public>:float = external {}

            @editable
            var Deform_Noise_Tile_U<public>:float = external {}

            @editable
            var Deform_Noise_Tile_V<public>:float = external {}

            @editable
            var Deform_Noise_Strength<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Colot_Test<public>:color = external {}

            var Noise_Tex<public>:texture = external {}

            var Noise_Tex_2<public>:texture = external {}

            var Deform_Noise_Tex<public>:texture = external {}

        MI_Object_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Object_3_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Fresnel_Ex<public>:float = external {}

            @editable
            var Fresnel_Reflect<public>:float = external {}

            @editable
            var Noise_Strength<public>:float = external {}

            @editable
            var Fresnel_Opacity<public>:float = external {}

            @editable
            var Noise_Contrast<public>:float = external {}

            @editable
            var Deform_Noise_Speed_X<public>:float = external {}

            @editable
            var Deform_Noise_Speed_Y<public>:float = external {}

            @editable
            var Deform_Noise_Tile_U<public>:float = external {}

            @editable
            var Deform_Noise_Tile_V<public>:float = external {}

            @editable
            var Deform_Noise_Strength<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Colot_Test<public>:color = external {}

            var Noise_Tex<public>:texture = external {}

            var Noise_Tex_2<public>:texture = external {}

            var Deform_Noise_Tex<public>:texture = external {}

        MI_Object_3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Object_4_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Fresnel_Ex<public>:float = external {}

            @editable
            var Fresnel_Reflect<public>:float = external {}

            @editable
            var Noise_Strength<public>:float = external {}

            @editable
            var Fresnel_Opacity<public>:float = external {}

            @editable
            var Noise_Contrast<public>:float = external {}

            @editable
            var Deform_Noise_Speed_X<public>:float = external {}

            @editable
            var Deform_Noise_Speed_Y<public>:float = external {}

            @editable
            var Deform_Noise_Tile_U<public>:float = external {}

            @editable
            var Deform_Noise_Tile_V<public>:float = external {}

            @editable
            var Deform_Noise_Strength<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Colot_Test<public>:color = external {}

            var Noise_Tex<public>:texture = external {}

            var Noise_Tex_2<public>:texture = external {}

            var Deform_Noise_Tex<public>:texture = external {}

        MI_Object_4<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Ring_1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Mask_Contrast<public>:float = external {}

            @editable
            var Mask_Speed<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_X<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_Y<public>:float = external {}

            @editable
            var Erosion_Strength<public>:float = external {}

            @editable
            var Erosion_Contrast<public>:float = external {}

            var Mask<public>:texture = external {}

            var (/<EMAIL>/Pekao_Bank/TycoonPack/Material/MI_Ring_1_material:)Texture<public>:texture = external {}

            var Erosion_Noise<public>:texture = external {}

        MI_Ring_1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_Sparks_1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/TycoonPack/Material/MI_Sparks_1_material:)Texture<public>:texture = external {}

        MI_Sparks_1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MM_Aura_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Deormation_Strength<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_X<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_Y<public>:float = external {}

            @editable
            var Erosion_Strength<public>:float = external {}

            @editable
            var Erosion_Contrast<public>:float = external {}

            @editable
            var Deformation_Noise_Tile_U<public>:float = external {}

            @editable
            var Deformation_Noise_Tile_V<public>:float = external {}

            @editable
            var Deformation_Noise_Speed_X<public>:float = external {}

            @editable
            var Deformation_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_y<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Fade_Distance<public>:float = external {}

            @editable
            var Outer_Mask_Length<public>:float = external {}

            @editable
            var Test_Color<public>:color = external {}

            var Main_Noise_Tex<public>:texture = external {}

            var Erosion_Noise<public>:texture = external {}

            var Param<public>:texture = external {}

        MM_Aura<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MM_Object_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Main_Noise_Speed_X<public>:float = external {}

            @editable
            var Main_Noise_Speed_Y<public>:float = external {}

            @editable
            var Main_Noise_Tile_U<public>:float = external {}

            @editable
            var Main_Noise_Tile_V<public>:float = external {}

            @editable
            var Fresnel_Ex<public>:float = external {}

            @editable
            var Fresnel_Reflect<public>:float = external {}

            @editable
            var Noise_Strength<public>:float = external {}

            @editable
            var Fresnel_Opacity<public>:float = external {}

            @editable
            var Noise_Contrast<public>:float = external {}

            @editable
            var Deform_Noise_Speed_X<public>:float = external {}

            @editable
            var Deform_Noise_Speed_Y<public>:float = external {}

            @editable
            var Deform_Noise_Tile_U<public>:float = external {}

            @editable
            var Deform_Noise_Tile_V<public>:float = external {}

            @editable
            var Deform_Noise_Strength<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Colot_Test<public>:color = external {}

            var Noise_Tex<public>:texture = external {}

            var Noise_Tex_2<public>:texture = external {}

            var Deform_Noise_Tex<public>:texture = external {}

        MM_Object<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MM_Ring_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Mask_Contrast<public>:float = external {}

            @editable
            var Mask_Speed<public>:float = external {}

            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_X<public>:float = external {}

            @editable
            var Erosion_Noise_Speed_Y<public>:float = external {}

            @editable
            var Erosion_Strength<public>:float = external {}

            @editable
            var Erosion_Contrast<public>:float = external {}

            var Mask<public>:texture = external {}

            var (/<EMAIL>/Pekao_Bank/TycoonPack/Material/MM_Ring_material:)Texture<public>:texture = external {}

            var Erosion_Noise<public>:texture = external {}

        MM_Ring<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MM_Sparks_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Emission_Strength<public>:float = external {}

            @editable
            var Emission_Contrast<public>:float = external {}

            @editable
            var Opacity_Strength<public>:float = external {}

            @editable
            var Opacity_Contrast<public>:float = external {}

            var (/<EMAIL>/Pekao_Bank/TycoonPack/Material/MM_Sparks_material:)Texture<public>:texture = external {}

        MM_Sparks<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        Upgrade_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Mesh
    Mesh := module:
        hammer_FBX<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Bubble<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        SM_Cyllinder_Round<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Particles
    Particles := module:
        # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Particles/Icons
        (/<EMAIL>/Pekao_Bank/TycoonPack/Particles:)Icons := module:
            P_Cautions<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Contracts<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Driving<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Edit<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Explosion<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_GoldBar<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_GoldBars<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Kart<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Message<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            (/<EMAIL>/Pekao_Bank/TycoonPack/Particles/Icons:)P_Money<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Pickaxes<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_PlaceHere<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Present<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Raction<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_RactionSad<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_RactionSmile<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_SettingsGear<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Sword<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Particles/Money
        Money := module:
            P_CoinExplosion<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_CoinExplosion10<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_CoinExplosion100<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_CoinExplosion20<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_CoinExplosion25<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_CoinExplosion5<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_CoinExplosion50<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_CoinExplosion500<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_CoinExplosion75<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            (/<EMAIL>/Pekao_Bank/TycoonPack/Particles/Money:)P_Money<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Plus100<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Plus200<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Plus50<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Plus500<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Particles/Notification
        Notification := module:
            P_ExclamationMarker<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_ExclamationMarker1<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_QuestionMarker<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Particles/Smoke
        (/<EMAIL>/Pekao_Bank/TycoonPack/Particles:)Smoke := module:
            P_Building<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Smoke<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_SmokeRealistic<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Particles/Upgrade
        (/<EMAIL>/Pekao_Bank/TycoonPack/Particles:)Upgrade := module:
            P_LevelUp<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_Upgrade<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Particles/Zone
        Zone := module:
            P_BuyZone<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

            P_BuyZone_BakedAtlas_Texture2D_0<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            P_ClaimZone<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Texture
    (/<EMAIL>/Pekao_Bank/TycoonPack:)Texture := module:
        buy<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        claim<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        Creative_Coin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        LEVELUP<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        MoneySprite<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_100b<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_100Coin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_10Coin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_200<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_20Coin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_25Coin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_50<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_500b<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_500Coin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_50Coin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_5Coin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_75Coin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Circle_5<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Clouds_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Clouds_02_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        T_Cyllinder_Base_4<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Cyllinder_Wall_3<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Glow_Super_Soft<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_32<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_33<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_36<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_38<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_53<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_54<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_58<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_60<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_65<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Noise_66<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Popup_Arrow_01<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Popup_Smoke_02<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_Rotation_Mask_1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/TycoonPack/Texture:)Upgrade<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/TycoonPack/Texture/Icons
        (/<EMAIL>/Pekao_Bank/TycoonPack/Texture:)Icons := module:
            arrow<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            arrow_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Cart<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Caution<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Contract<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            DollarSign<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            DollarSign_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Driving_Wheel<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/TycoonPack/Texture/Icons:)Explosion<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Gear<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Gold_Bars<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Message<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Pencil<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Pickaxe<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Place_Here<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Present<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Swiord<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_Mask_1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_Mask_1_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            T_Mask_3<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/UI
(/<EMAIL>/Pekao_Bank:)UI := module:
    rank_0<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/Upgrade_Prices
Upgrade_Prices := module:
    # Module import path: /<EMAIL>/Pekao_Bank/Upgrade_Prices/MasterMaterial
    MasterMaterial := module:
        ClaimBankRotToPlayer_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Scale<public>:float = external {}

            var MyTexture<public>:texture = external {}

        ClaimBankRotToPlayer<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        powrotdocheckpointu_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Scale<public>:float = external {}

            var MyTexture<public>:texture = external {}

        powrotdocheckpointu<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        restprogresu_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Scale<public>:float = external {}

            var MyTexture<public>:texture = external {}

        restprogresu<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SimplestAmt_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Scale<public>:float = external {}

            var MyTexture<public>:texture = external {}

        SimplestAmt<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/Upgrade_Prices/Textures
    (/<EMAIL>/Pekao_Bank/Upgrade_Prices:)Textures := module:
        muzyka<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        nagroda<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VArrowSystem
VArrowSystem := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VArrowSystem/Blueprints
    (/<EMAIL>/Pekao_Bank/VArrowSystem:)Blueprints := module:
        M_ArrowTransparent_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var YawRotation<public>:float = external {}

            @editable
            var X<public>:color = external {}

            @editable
            var Y<public>:color = external {}

            @editable
            var Z<public>:color = external {}

        M_ArrowTransparent<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        MI_ArrowTransparent_Inst_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var YawRotation<public>:float = external {}

            @editable
            var X<public>:color = external {}

            @editable
            var Y<public>:color = external {}

            @editable
            var Z<public>:color = external {}

        MI_ArrowTransparent_Inst<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/VArrowSystem/Meshes
    (/<EMAIL>/Pekao_Bank/VArrowSystem:)Meshes := module:
        arrow2d<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VAudio
VAudio := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VAudio/Economy
    Economy := module:
        Economic_Turn_Negative_A<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Economic_Turn_Negative_B<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Economic_Turn_Negative_C<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Economic_Turn_Negative_D<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Economic_Turn_Negative_E<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Economic_Turn_Positive_A<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Economic_Turn_Positive_B<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Economic_Turn_Positive_C<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Economic_Turn_Positive_D<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Economic_Turn_Positive_E<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Money_Earned_A<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Money_Earned_B<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Money_Earned_C<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Money_Earned_D<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Money_Earned_E<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Sign_Legislation_A<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/VAudio/Unlock
    Unlock := module:
        Upgrade_Building_A<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Upgrade_Building_B<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Upgrade_Building_C<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Upgrade_Building_D<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

        Upgrade_Building_E<scoped {/<EMAIL>/Pekao_Bank}>:sound_wave = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VBuyables
VBuyables := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VBuyables/Assets
    (/<EMAIL>/Pekao_Bank/VBuyables:)Assets := module:
        NS_Head<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        NS_Head1<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        (/<EMAIL>/Pekao_Bank/VBuyables/Assets:)NS_SampleCosmetic<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VChestsSystem
VChestsSystem := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VChestsSystem/Assets
    (/<EMAIL>/Pekao_Bank/VChestsSystem:)Assets := module:
        # Module import path: /<EMAIL>/Pekao_Bank/VChestsSystem/Assets/Chests
        Chests := module:
            chest_large_Anim<scoped {/<EMAIL>/Pekao_Bank}>:animation_sequence = external {}

            chest_large_col2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            M_chest_large_col<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VCosmetics
VCosmetics := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VCosmetics/Assets
    (/<EMAIL>/Pekao_Bank/VCosmetics:)Assets := module:
        NS_Chestplate_Cosmetic<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        NS_Moneybag_Cosmetic<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        (/<EMAIL>/Pekao_Bank/VCosmetics/Assets:)NS_SampleCosmetic<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

        NS_SantaHat_Cosmetic<scoped {/<EMAIL>/Pekao_Bank}>:particle_system = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VCustomBillboard
VCustomBillboard := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VCustomBillboard/Assets
    (/<EMAIL>/Pekao_Bank/VCustomBillboard:)Assets := module:
        plane_for_text_billboard<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials
        TextMaterials := module:
            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_0_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_0<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_3_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_4_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_4<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_5_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_5<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_6_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_6<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_7_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_7<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_8_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_8<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_9_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_9<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_A_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_A<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Al_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Al<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_B_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_B<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_C_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_C<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Cash_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Cash<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Ci_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Ci<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Comma_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Comma<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_D_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_D<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Dot_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Dot<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_E_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_E<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_El_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_El<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_F_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_F<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_G_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_G<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_H_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_H<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_I_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_I<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_J_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_J<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_K_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_K<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_L_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_L<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Ly_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Ly<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_M_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_M<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_N_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_N<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Ni_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Ni<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_O_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_O<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Oo_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Oo<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_P_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_P<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Q_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Q<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_R_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_R<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_S_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_S<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Si_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Si<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Slash_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Slash<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_T_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_T<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Token_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Token<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_U_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_U<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_V_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_V<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_W_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_W<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_X_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_X<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Y_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Y<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Z_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Z<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Zi_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Zi<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Zy_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterials:)MI_RotateToPlayer_Text_Zy<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen
        TextMaterialsGreen := module:
            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_0_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_0<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_3_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_4_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_4<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_5_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_5<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_6_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_6<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_7_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_7<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_8_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_8<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_9_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_9<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_A_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_A<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Al_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Al<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_B_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_B<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_C_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_C<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Cash_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Cash<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Ci_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Ci<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Comma_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Comma<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_D_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_D<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Dot_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Dot<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_E_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_E<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_El_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_El<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_F_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_F<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_G_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_G<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_H_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_H<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_I_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_I<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_J_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_J<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_K_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_K<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_L_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_L<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Ly_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Ly<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_M_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_M<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_N_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_N<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Ni_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Ni<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_O_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_O<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Oo_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Oo<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_P_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_P<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Q_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Q<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_R_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_R<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_S_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_S<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Si_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Si<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Slash_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Slash<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_T_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_T<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Token_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Token<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_U_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_U<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_V_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_V<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_W_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_W<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_X_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_X<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Y_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Y<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Z_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Z<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Zi_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Zi<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Zy_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextMaterialsGreen:)MI_RotateToPlayer_Text_Zy<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/VCustomBillboard/Assets/TextTextures
        TextTextures := module:
            TT<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT0<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT3<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT4<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT5<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT6<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT7<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT8<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TT9<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTA<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTAl<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTB<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTC<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTCash<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTCi<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTComma<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTD<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTDot<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTE<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTEl<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTF<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTG<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTH<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTI<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTJ<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTK<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTL<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTLy<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTM<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTN<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTNi<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTO<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTOo<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTP<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTQ<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTR<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTS<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTSi<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTSlash<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTT<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTU<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTV<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTW<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTX<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTY<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTZ<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTZi<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            TTZy<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VGiga
VGiga := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VGiga/Assets
    (/<EMAIL>/Pekao_Bank/VGiga:)Assets := module:
        # Module import path: /<EMAIL>/Pekao_Bank/VGiga/Assets/GigaTextures
        GigaTextures := module:
            T_horizontal_background_fade_notifs_ui<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            White4x4<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            White4x4_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/VGiga/Assets/Mats
        Mats := module:
            AreaBoxTransparent_M_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Alpha<public>:float = external {}

                @editable
                var Color<public>:color = external {}

            AreaBoxTransparent_M<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            AreaBoxTransparent_MI_Orange_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Alpha<public>:float = external {}

                @editable
                var Color<public>:color = external {}

            AreaBoxTransparent_MI_Orange<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            DeviceTransparent_MI_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Color<public>:color = external {}

            DeviceTransparent_MI<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            Invisible_M<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            M_RotateToPlayer_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            M_RotateToPlayer<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            M_RotateToPlayerText<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            M_TransparentSlightly_DoubleSide_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Alpha<public>:float = external {}

                @editable
                var Color<public>:color = external {}

            M_TransparentSlightly_DoubleSide<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_RotateToPlayerExcludeZ_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            MI_RotateToPlayerExcludeZ<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            PlaneForRotateToPlayer<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

            Solid_M_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Color<public>:color = external {}

            Solid_M<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/VGiga/Assets/Meshes
        (/<EMAIL>/Pekao_Bank/VGiga/Assets:)Meshes := module:
            Rectangle_Forward<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VHouseSystem
VHouseSystem := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VHouseSystem/Materials
    (/<EMAIL>/Pekao_Bank/VHouseSystem:)Materials := module:
        M_GradientTransparent_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

        M_GradientTransparent<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_GradientTransparent_Blue_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

        M_GradientTransparent_Blue<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_GradientTransparent_Orange_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

        M_GradientTransparent_Orange<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_GradientTransparent_Red_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

        M_GradientTransparent_Red<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        M_GradientTransparentZTest_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var Color<public>:color = external {}

        M_GradientTransparentZTest<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VLeaderboard
VLeaderboard := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VLeaderboard/UI
    (/<EMAIL>/Pekao_Bank/VLeaderboard:)UI := module:
        LeaderboardLandingPageUi<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


# Module import path: /<EMAIL>/Pekao_Bank/VLocalization
(/<EMAIL>/Pekao_Bank:)VLocalization := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VLocalization/Assets
    (/<EMAIL>/Pekao_Bank/VLocalization:)Assets := module:
        (/<EMAIL>/Pekao_Bank/VLocalization/Assets:)T_WelcomeScreen<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/VLocalization/Assets:)WB_LangSelect<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_LangSelect1<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        # Module import path: /<EMAIL>/Pekao_Bank/VLocalization/Assets/Textures
        (/<EMAIL>/Pekao_Bank/VLocalization/Assets:)Textures := module:
            Flag_of_Poland_512<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            Flag_of_the_United_Kingdom_512<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VMapCode
VMapCode := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VMapCode/Textures
    (/<EMAIL>/Pekao_Bank/VMapCode:)Textures := module:
        MapCodeTexture<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        MapCodeTextureLeft<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VMinigames
(/<EMAIL>/Pekao_Bank:)VMinigames := module:
    (/<EMAIL>/Pekao_Bank/VMinigames:)buildtime<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    T_BgEndTime<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    T_BtnBg<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    T_Checkmark<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    T_QuizBg<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    T_XFailed<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    WBP_OneButtonEndTime<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WBP_Quiz<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WBP_Quiz2_Bg<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WBP_QuizBadAnswer<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WBP_QuizGoodAnswer<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WBP_QuizOneButton<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WBP_Verse_Quiz<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    # Module import path: /<EMAIL>/Pekao_Bank/VMinigames/Assets
    (/<EMAIL>/Pekao_Bank/VMinigames:)Assets := module:
        M_Apple_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
            @editable
            var BaseColorTexture_Rotation<public>:float = external {}

            @editable
            var BaseColorTexture_TexCoord<public>:float = external {}

            @editable
            var MetallicRoughnessTexture_Rotation<public>:float = external {}

            @editable
            var MetallicRoughnessTexture_TexCoord<public>:float = external {}

            @editable
            var MetallicFactor<public>:float = external {}

            @editable
            var RoughnessFactor<public>:float = external {}

            @editable
            var NormalTexture_Rotation<public>:float = external {}

            @editable
            var NormalTexture_TexCoord<public>:float = external {}

            @editable
            var NormalScale<public>:float = external {}

            @editable
            var OcclusionTexture_Rotation<public>:float = external {}

            @editable
            var OcclusionTexture_TexCoord<public>:float = external {}

            @editable
            var OcclusionStrength<public>:float = external {}

            @editable
            var IOR<public>:float = external {}

            @editable
            var SpecularTexture_Rotation<public>:float = external {}

            @editable
            var SpecularTexture_TexCoord<public>:float = external {}

            @editable
            var SpecularFactor<public>:float = external {}

            @editable
            var EmissiveTexture_Rotation<public>:float = external {}

            @editable
            var EmissiveTexture_TexCoord<public>:float = external {}

            @editable
            var EmissiveStrength<public>:float = external {}

            @editable
            var AlphaCutoff<public>:float = external {}

            @editable
            var IridescenceIOR<public>:float = external {}

            @editable
            var IridescenceFactor<public>:float = external {}

            @editable
            var IridescenceThicknessMinimum<public>:float = external {}

            @editable
            var IridescenceThicknessMaximum<public>:float = external {}

            @editable
            var IridescenceTexture_Rotation<public>:float = external {}

            @editable
            var IridescenceTexture_TexCoord<public>:float = external {}

            @editable
            var IridescenceThicknessTexture_Rotation<public>:float = external {}

            @editable
            var IridescenceThicknessTexture_TexCoord<public>:float = external {}

            @editable
            var AnisotropyStrength<public>:float = external {}

            @editable
            var AnisotropyRotation<public>:float = external {}

            @editable
            var AnisotropyTexture_Rotation<public>:float = external {}

            @editable
            var AnisotropyTexture_TexCoord<public>:float = external {}

            @editable
            var BaseColorFactor<public>:color = external {}

            @editable
            var MetallicRoughnessTexture_OffsetScale<public>:color = external {}

            @editable
            var BaseColorTexture_OffsetScale<public>:color = external {}

            @editable
            var NormalTexture_OffsetScale<public>:color = external {}

            @editable
            var OcclusionTexture_OffsetScale<public>:color = external {}

            @editable
            var SpecularTexture_OffsetScale<public>:color = external {}

            @editable
            var EmissiveTexture_OffsetScale<public>:color = external {}

            @editable
            var EmissiveFactor<public>:color = external {}

            @editable
            var SpecularTexture_TilingMethod<public>:color = external {}

            @editable
            var OcclusionTexture_TilingMethod<public>:color = external {}

            @editable
            var NormalTexture_TilingMethod<public>:color = external {}

            @editable
            var EmissiveTexture_TilingMethod<public>:color = external {}

            @editable
            var MetallicRoughnessTexture_TilingMethod<public>:color = external {}

            @editable
            var BaseColorTexture_TilingMethod<public>:color = external {}

            @editable
            var IridescenceTexture_TilingMethod<public>:color = external {}

            @editable
            var IridescenceThicknessTexture_TilingMethod<public>:color = external {}

            @editable
            var IridescenceTexture_OffsetScale<public>:color = external {}

            @editable
            var IridescenceThicknessTexture_OffsetScale<public>:color = external {}

            @editable
            var AnisotropyTexture_OffsetScale<public>:color = external {}

            @editable
            var AnisotropyTexture_TilingMethod<public>:color = external {}

            var BaseColorTexture<public>:texture = external {}

            var MetallicRoughnessTexture<public>:texture = external {}

            var NormalTexture<public>:texture = external {}

            var OcclusionTexture<public>:texture = external {}

            var SpecularTexture<public>:texture = external {}

            var EmissiveTexture<public>:texture = external {}

            var IridescenceTexture<public>:texture = external {}

            var IridescenceThicknessTexture<public>:texture = external {}

            var AnisotropyTexture<public>:texture = external {}

        M_Apple<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        SM_Apple<scoped {/<EMAIL>/Pekao_Bank}>:mesh = external {}

        T_Apple<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_MinigamePointsResult<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_MinigamePointsResultPL<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        (/<EMAIL>/Pekao_Bank/VMinigames/Assets:)WBP_MinigameResult<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


# Module import path: /<EMAIL>/Pekao_Bank/VNews
VNews := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VNews/Assets
    (/<EMAIL>/Pekao_Bank/VNews:)Assets := module:
        WB_News<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_News1<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


# Module import path: /<EMAIL>/Pekao_Bank/VNotifications
(/<EMAIL>/Pekao_Bank:)VNotifications := module:
    T_TopPoints<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    (/<EMAIL>/Pekao_Bank/VNotifications:)WBP_BigTopNotification<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WBP_BigTopNotification1<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WidgeManyResouceNotifs<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


# Module import path: /<EMAIL>/Pekao_Bank/VPhone
(/<EMAIL>/Pekao_Bank:)VPhone := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VPhone/Assets
    (/<EMAIL>/Pekao_Bank/VPhone:)Assets := module:
        WB_Popup_PhoneAchievments2_Verse<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_Popup_PhoneCards1<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_Popup_PhoneInvest2<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_Popup_PhoneInvest5_Verse<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_Popup_PhoneMainMenu3<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_Popup_PhoneMainMenuFirst<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_Popup_PhoneReg1<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_Popup_PhoneReg2<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_Popup_PhoneReg2_Avatar<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WB_Popup_PhoneReg3<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        # Module import path: /<EMAIL>/Pekao_Bank/VPhone/Assets/Materials
        (/<EMAIL>/Pekao_Bank/VPhone/Assets:)Materials := module:
            card_hidden<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            eng<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            eng_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            logopekao<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            logopekao_Mat<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            M_Button_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Alpha<public>:float = external {}

                var Main<public>:texture = external {}

            M_Button<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            M_PlayerAvatar_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var AvatarIcon<public>:texture = external {}

            M_PlayerAvatar<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_ButtonCard1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Alpha<public>:float = external {}

                var Main<public>:texture = external {}

            MI_ButtonCard1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_ButtonCard2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Alpha<public>:float = external {}

                var Main<public>:texture = external {}

            MI_ButtonCard2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_ButtonTransparent_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Alpha<public>:float = external {}

                var Main<public>:texture = external {}

            MI_ButtonTransparent<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_PlayerAvatar_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                var AvatarIcon<public>:texture = external {}

            MI_PlayerAvatar<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            pekao_card_gold_fx<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_test_avatar<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            WBP_ButtonCard1<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


            WBP_ButtonCard2<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


            WBP_ButtonTransparent<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


            WBP_ButtonX<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        # Module import path: /<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures
        (/<EMAIL>/Pekao_Bank/VPhone/Assets:)VTextures := module:
            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_Achievements<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_CardSelect<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_Deposit<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_Deposit2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_MainMenu<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_MainMenuFirst<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_MiniPhone<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_PhoneQuestBg<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_PhoneQuestDone<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_Reg1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_Reg2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_Reg3<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            (/<EMAIL>/Pekao_Bank/VPhone/Assets/VTextures:)T_SavingsBg<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VPopup
VPopup := module:
    T_PopupButton1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    T_PopupButton2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    WBP_Popup1<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WBP_Popup2<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


# Module import path: /<EMAIL>/Pekao_Bank/VQuestSystem
VQuestSystem := module:
    WBP_QuestsListGen<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    # Module import path: /<EMAIL>/Pekao_Bank/VQuestSystem/Assets
    (/<EMAIL>/Pekao_Bank/VQuestSystem:)Assets := module:
        T_QuestBg<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        WBP_Quest<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


# Module import path: /<EMAIL>/Pekao_Bank/VRaceMinigame
VRaceMinigame := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VRaceMinigame/UI
    (/<EMAIL>/Pekao_Bank/VRaceMinigame:)UI := module:
        WBP_RaceCoinsBar<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WBP_RaceCoinsHudMessage<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


# Module import path: /<EMAIL>/Pekao_Bank/VResourcesSystem
VResourcesSystem := module:
    # Module import path: /<EMAIL>/Pekao_Bank/VResourcesSystem/Assets
    (/<EMAIL>/Pekao_Bank/VResourcesSystem:)Assets := module:
        SaveTimer<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        Screenshott<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_PlayerCard<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_PlayerCard1<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_PlayerCard2<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_PlayerCard3<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_PlayerCard4<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_PlayerCard5<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_SaveCompleted<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        T_SaveTimerBg<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

        WBP_SaveCompleted<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WidgetResourceMiniPhone<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WidgetResources<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        WidgetResources_HudController<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


        # Module import path: /<EMAIL>/Pekao_Bank/VResourcesSystem/Assets/Panels
        Panels := module:
            MI_RotateToPlayer_Card0_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            MI_RotateToPlayer_Card0<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_RotateToPlayer_Card1_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            MI_RotateToPlayer_Card1<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_RotateToPlayer_Card2_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            MI_RotateToPlayer_Card2<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_RotateToPlayer_Card3_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            MI_RotateToPlayer_Card3<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_RotateToPlayer_Card4_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            MI_RotateToPlayer_Card4<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

            MI_RotateToPlayer_Card5_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
                @editable
                var Tint<public>:color = external {}

                var BaseColor<public>:texture = external {}

            MI_RotateToPlayer_Card5<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

        # Module import path: /<EMAIL>/Pekao_Bank/VResourcesSystem/Assets/TTextures
        TTextures := module:
            med_esp_forui<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_GoldCoin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_GoldCoinMargin<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_Token<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

            T_TokenPadding<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VTextures
(/<EMAIL>/Pekao_Bank:)VTextures := module:
    greenish4x4<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    M_SpriteGlow_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
        @editable
        var Width<public>:float = external {}

        @editable
        var Period<public>:float = external {}

        @editable
        var Sharpness<public>:float = external {}

        @editable
        var Delay<public>:float = external {}

        @editable
        var BandColor<public>:color = external {}

        var (/<EMAIL>/Pekao_Bank/VTextures/M_SpriteGlow_material:)Texture<public>:texture = external {}

    M_SpriteGlow<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    M_SpriteGlow_Utils_material<scoped {/<EMAIL>/Pekao_Bank}> := class<final><scoped {/<EMAIL>/Pekao_Bank}>(material):
        @editable
        var Width<public>:float = external {}

        @editable
        var Period<public>:float = external {}

        @editable
        var Sharpness<public>:float = external {}

        @editable
        var Delay<public>:float = external {}

        @editable
        var BandColor<public>:color = external {}

        var (/<EMAIL>/Pekao_Bank/VTextures/M_SpriteGlow_Utils_material:)Texture<public>:texture = external {}

    M_SpriteGlow_Utils<scoped {/<EMAIL>/Pekao_Bank}>:material = external {}

    Orange4x4<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    Shotguns<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    Utils<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    # Module import path: /<EMAIL>/Pekao_Bank/VTextures/UI
    (/<EMAIL>/Pekao_Bank/VTextures:)UI := module:
        T_Panel<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

# Module import path: /<EMAIL>/Pekao_Bank/VTimer
VTimer := module:
    WBP_Timer<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


# Module import path: /<EMAIL>/Pekao_Bank/VTimeRewards
(/<EMAIL>/Pekao_Bank:)VTimeRewards := module:
    (/<EMAIL>/Pekao_Bank/VTimeRewards:)T_TimeRewardBg<scoped {/<EMAIL>/Pekao_Bank}>:texture = external {}

    WBP_Verse_RewardsClaim<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):


    WBP_Verse_RewardsOnScreenTimer<scoped {/<EMAIL>/Pekao_Bank}> := class<public>(widget):
