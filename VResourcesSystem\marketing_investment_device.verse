using { /Fortnite.com/Devices }
using { /Fortnite.com/FortPlayerUtilities }
using { /Fortnite.com/Characters }
using { /Fortnite.com/AI }
using { /Verse.org/Simulation }
using { VGiga }
using { VCustomBillboard }
using { VNotifications }

marketing_player_data := class:
    var MarketingInvestment:int = 0
    var CustomerCount:int = 0
    var Agent:agent

    SetMarketingInvestment<public>(NewInvestment:int)<transacts>:void=
        set MarketingInvestment = NewInvestment

    SetCustomerCount<public>(NewCount:int)<transacts>:void=
        set CustomerCount = NewCount

    InitializeFromSave<public>(SaveSystem:save_system, PlayerAgent:agent)<transacts>:void=
        set MarketingInvestment = SaveSystem.GetIntStat(PlayerAgent, "MarketingInvestment")
        set CustomerCount = SaveSystem.GetIntStat(PlayerAgent, "CustomerCount")

marketing_investment_device<public> := class(auto_creative_device, i_init_async):
    @editable InvestButton:button_device = button_device{}
    @editable InvestmentBillboard:custom_billboard_devic = custom_billboard_devic{}
    @editable CustomerBillboard:custom_billboard_devic = custom_billboard_devic{}
    @editable InvestmentCost:int = 1000

    var Resources:?resources_manager = false
    var SaveSystem:?save_system = false
    var Notifications:?notifications_system = false
    var PlayerEvents:?player_events_manager_devic = false
    var Loc:i_localization = empty_i_localization{}

    # Cached player data - load once, update in memory, save on change
    var PlayerMarketingData:[agent]marketing_player_data = map{}

    # Customer acquisition and revenue constants - IMPROVED BALANCE
    # Target: $100k investment + 30 minutes = ~$50/s revenue
    # With sqrt scaling: sqrt(100000/50000) = sqrt(2) ≈ 1.41
    # Rate = 0.5 * 1.41 = 0.705 customers/sec
    # After 30 min: 0.705 * 1800 = 1269 customers
    # Revenue: 1269 * 0.04 = ~50.76/s ✓
    var BaseCustomerAcquisitionRate:float = 0.5 # base customers per second
    var CustomerRevenueRate:float = 0.04 # dollars per second per customer (consistent small amount)
    var LogarithmicScalingFactor:float = 50000.0 # controls how quickly customer growth plateaus
    var MaxCustomerGrowthRate:float = 3.0 # maximum customers that can be gained per second

    InitAsync<override>(Container:vcontainer)<suspends>:void=
        set Resources = Container.ResolveOp[resources_manager] or Err()
        set SaveSystem = Container.ResolveOp[save_system] or Err()
        set Notifications = Container.ResolveOp[notifications_system] or Err()
        set PlayerEvents = Container.ResolveOp[player_events_manager_devic] or Err()
        set Loc = Container.Resolve_i_localization()

        spawn. HandleButtonActivations()
        spawn. UpdateCustomers()
        spawn. UpdateBillboards()
        spawn. HandlePlayerEvents()

        # Test balance on startup
        TestBalance()

    HandlePlayerEvents()<suspends>:void=
        race:
            loop:
                PlayerJoined := PlayerEvents.G().PlayerAddedOncePerGame.Await()
                InitializePlayerData(PlayerJoined)
            loop:
                PlayerLeft := PlayerEvents.G().PlayerRemovedOncePerGame.Await()
                CleanupPlayerData(PlayerLeft)

    InitializePlayerData(Agent:agent)<transacts>:void=
        # Load marketing data from save system once when player joins
        PlayerData := marketing_player_data{Agent := Agent}
        PlayerData.InitializeFromSave(SaveSystem.G(), Agent)

        if. set PlayerMarketingData[Agent] = PlayerData
        Print("Initialized marketing data for player - Investment: ${PlayerData.MarketingInvestment}, Customers: {PlayerData.CustomerCount}")

    CleanupPlayerData(Agent:agent):void=
        # Remove player data when they leave
        set PlayerMarketingData = PlayerMarketingData.WithRemoved(Agent)

    UpdateMarketingInvestment(Agent:agent, NewInvestment:int)<transacts>:void=
        if(PlayerData := PlayerMarketingData[Agent]):
            PlayerData.SetMarketingInvestment(NewInvestment)
            # Save to persistent storage
            SaveSystem.G().SetIntStat(Agent, "MarketingInvestment", NewInvestment)

    UpdateCustomerCount(Agent:agent, NewCustomerCount:int)<transacts>:void=
        if(PlayerData := PlayerMarketingData[Agent]):
            PlayerData.SetCustomerCount(NewCustomerCount)
            # Save to persistent storage
            SaveSystem.G().SetIntStat(Agent, "CustomerCount", NewCustomerCount)



    HandleButtonActivations()<suspends>:void=
        loop:
            Agent := InvestButton.InteractedWithEvent.Await()
            TookGold := Resources.G().TryToTakeGold(Agent, InvestmentCost)
            if(TookGold?):
                # Add to marketing investment using cached data
                if(PlayerData := PlayerMarketingData[Agent]):
                    NewInvestment := PlayerData.MarketingInvestment + InvestmentCost
                    UpdateMarketingInvestment(Agent, NewInvestment)

                    # Show notification
                    Notifications.G().ShowCenterNotification(Agent, "Marketing investment increased! Total: ${NewInvestment.ToShortNumberString()}".ToMessage(), 3.0)
                else:
                    NewInvestment := InvestmentCost
                    UpdateMarketingInvestment(Agent, NewInvestment)
                    Notifications.G().ShowCenterNotification(Agent, "Marketing investment started! Total: ${NewInvestment.ToShortNumberString()}".ToMessage(), 3.0)
            else:
                # Not enough gold
                Notifications.G().ShowCenterNotification(Agent, "Not enough gold for marketing investment!".ToMessage(), 2.0)

    UpdateCustomers()<suspends>:void=
        loop:
            Sleep(1.0) # Update every second

            # Use cached player data instead of save system calls
            for(Agent -> PlayerData : PlayerMarketingData):
                CurrentInvestment := PlayerData.MarketingInvestment
                CurrentCustomers := PlayerData.CustomerCount

                if(CurrentInvestment > 0):
                    # Calculate customer acquisition rate with logarithmic-like scaling
                    # Formula: rate = base_rate * sqrt(investment / scaling_factor)
                    # This creates diminishing returns where customer growth plateaus
                    SqrtFactor := Sqrt(CurrentInvestment / LogarithmicScalingFactor)
                    CustomerAcquisitionRate := Min(BaseCustomerAcquisitionRate * SqrtFactor, MaxCustomerGrowthRate)

                    # Add customers (fractional customers accumulate over time)
                    NewCustomers := CurrentCustomers + (Round[CustomerAcquisitionRate] or 0)
                    if(NewCustomers > CurrentCustomers):
                        UpdateCustomerCount(Agent, NewCustomers)

                # Generate revenue from existing customers
                if(CurrentCustomers > 0):
                    Revenue := Round[CurrentCustomers * CustomerRevenueRate] or 0
                    if(Revenue > 0):
                        Resources.G().GiveGoldText(Agent, Revenue, "Customers")

    UpdateBillboards()<suspends>:void=
        loop:
            Sleep(2.0) # Update billboards every 2 seconds

            # Use cached player data instead of save system calls
            for(Agent -> PlayerData : PlayerMarketingData):
                CurrentInvestment := PlayerData.MarketingInvestment
                CurrentCustomers := PlayerData.CustomerCount

                # Update investment billboard (right side)
                InvestmentBillboard.SetText("{Loc.G(Agent, "Marketing Investment")}\n${CurrentInvestment.ToShortNumberString()}")

                # Update customer billboard (left side)
                CustomerBillboard.SetText("{Loc.G(Agent, "Customers")}\n{CurrentCustomers.ToShortNumberString()}")

    # Calculate expected revenue per second for balancing
    CalculateRevenuePerSecond<public>(Investment:int, Customers:int):float=
        Customers * CustomerRevenueRate

    # Calculate target customers for given investment and time
    CalculateTargetCustomers<public>(Investment:int, TimeInSeconds:float):int=
        if(Investment <= 0):
            return 0

        # Use same logarithmic-like scaling as in UpdateCustomers
        SqrtFactor := Sqrt(Investment / LogarithmicScalingFactor)
        CustomerAcquisitionRate := Min(BaseCustomerAcquisitionRate * SqrtFactor, MaxCustomerGrowthRate)

        Round[CustomerAcquisitionRate * TimeInSeconds] or 0

    # Test balance calculations with time simulation
    TestBalance<public>():void=
        Print("=== Marketing Investment Balance Test ===")

        # Test basic balance first
        TestBasicBalance()

        # Test time simulation (0.1 second = 1 minute)
        TestTimeSimulation()

        # Test logarithmic scaling
        TestLogarithmicScaling()

    TestBasicBalance():void=
        # Test case 1: $100,000 investment + 30 minutes (1800 seconds) should give ~$50/s
        Investment100k := 100000
        TimeInSeconds := 1800.0 # 30 minutes

        Customers := CalculateTargetCustomers(Investment100k, TimeInSeconds)
        RevenuePerSecond := CalculateRevenuePerSecond(Investment100k, Customers)

        Print("--- Basic Balance Test ---")
        Print("Investment: ${Investment100k}")
        Print("Time: {TimeInSeconds} seconds (30 minutes)")
        Print("Expected customers: {Customers}")
        Print("Expected revenue per second: ${RevenuePerSecond}")
        Print("Target revenue per second: $50")
        Print("Balance ratio: {RevenuePerSecond / 50.0}")

    TestTimeSimulation():void=
        Print("--- Time Simulation Test (0.1s = 1 minute) ---")

        var SimulatedInvestment:int = 0
        var SimulatedCustomers:int = 0
        var TotalRevenue:int = 0

        # Simulate 30 minutes of real time (3 seconds simulation time)
        # Player invests $10,000 every 5 minutes (0.5 seconds simulation time)
        InvestmentAmount := 10000
        InvestmentInterval := 5 # Every 5 simulation steps (5 minutes real time)

        Print("Simulating 30 minutes with $10k investments every 5 minutes:")
        Print("Time(min) | Investment | Customers | Revenue/s | Total Revenue")

        for(Step := 0..30): # 30 steps = 30 minutes
            # Invest every 5 minutes (simple check without modulo)
            if(Step = 5 or Step = 10 or Step = 15 or Step = 20 or Step = 25 or Step = 30):
                set SimulatedInvestment += InvestmentAmount
                Print("INVESTMENT: +${InvestmentAmount} (Total: ${SimulatedInvestment})")

            # Calculate customer acquisition for this minute
            if(SimulatedInvestment > 0):
                SqrtFactor := Sqrt(SimulatedInvestment / LogarithmicScalingFactor)
                CustomerAcquisitionRate := Min(BaseCustomerAcquisitionRate * SqrtFactor, MaxCustomerGrowthRate)
                NewCustomers := Round[CustomerAcquisitionRate * 60.0] or 0 # 60 seconds per minute
                set SimulatedCustomers += NewCustomers

            # Calculate revenue for this minute
            RevenueThisMinute := Round[SimulatedCustomers * CustomerRevenueRate * 60.0] or 0 # 60 seconds
            set TotalRevenue += RevenueThisMinute
            RevenuePerSecond := SimulatedCustomers * CustomerRevenueRate

            Print("Step {Step}: Investment ${SimulatedInvestment}, Customers {SimulatedCustomers}, Revenue/s ${RevenuePerSecond}, Total ${TotalRevenue}")

    TestLogarithmicScaling():void=
        Print("--- Logarithmic Scaling Test ---")
        Print("Testing how customer acquisition scales with investment:")
        Print("Investment | Customers/sec | Revenue/sec | Efficiency")

        TestInvestments := array{1000, 5000, 10000, 25000, 50000, 100000, 250000, 500000, 1000000}

        for(Investment : TestInvestments):
            SqrtFactor := Sqrt(Investment / LogarithmicScalingFactor)
            CustomerRate := Min(BaseCustomerAcquisitionRate * SqrtFactor, MaxCustomerGrowthRate)

            # Simulate customers after 30 minutes
            Customers := CalculateTargetCustomers(Investment, 1800.0)
            RevenuePerSec := Customers * CustomerRevenueRate
            Efficiency := if(Investment > 0):
                RevenuePerSec / (Investment / 1000.0)
            else:
                0.0

            Print("Investment ${Investment}: Rate {CustomerRate}, Revenue/s ${RevenuePerSec}, Efficiency {Efficiency}")
