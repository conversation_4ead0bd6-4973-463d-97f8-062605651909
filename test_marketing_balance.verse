using { /Verse.org/Simulation }

# Simple test to verify marketing investment balance
test_marketing_balance := class:
    
    # Same constants as in marketing_investment_device
    BaseCustomerAcquisitionRate:float = 0.5
    CustomerRevenueRate:float = 0.04
    LogarithmicScalingFactor:float = 50000.0
    MaxCustomerGrowthRate:float = 3.0
    
    # Calculate target customers for given investment and time
    CalculateTargetCustomers(Investment:int, TimeInSeconds:float):int=
        if(Investment <= 0):
            return 0

        # Use same logarithmic-like scaling as in UpdateCustomers
        SqrtFactor := Sqrt(Investment / LogarithmicScalingFactor)
        CustomerAcquisitionRate := Min(BaseCustomerAcquisitionRate * SqrtFactor, MaxCustomerGrowthRate)

        Round[CustomerAcquisitionRate * TimeInSeconds] or 0
    
    # Calculate expected revenue per second for balancing
    CalculateRevenuePerSecond(Investment:int, Customers:int):float=
        Customers * CustomerRevenueRate
    
    # Test the balance
    TestBalance():void=
        Print("=== Marketing Investment Balance Test ===")
        
        # Test case 1: $100,000 investment + 30 minutes (1800 seconds) should give ~$50/s
        Investment100k := 100000
        TimeInSeconds := 1800.0 # 30 minutes

        Customers := CalculateTargetCustomers(Investment100k, TimeInSeconds)
        RevenuePerSecond := CalculateRevenuePerSecond(Investment100k, Customers)

        Print("--- Basic Balance Test ---")
        Print("Investment: ${Investment100k}")
        Print("Time: {TimeInSeconds} seconds (30 minutes)")
        Print("Expected customers: {Customers}")
        Print("Expected revenue per second: ${RevenuePerSecond}")
        Print("Target revenue per second: $50")
        Print("Balance ratio: {RevenuePerSecond / 50.0}")
        
        # Test different investment levels
        Print("--- Investment Scaling Test ---")
        TestInvestments := array{1000, 5000, 10000, 25000, 50000, 100000, 250000, 500000, 1000000}
        
        for(Investment : TestInvestments):
            SqrtFactor := Sqrt(Investment / LogarithmicScalingFactor)
            CustomerRate := Min(BaseCustomerAcquisitionRate * SqrtFactor, MaxCustomerGrowthRate)
            
            # Simulate customers after 30 minutes
            Customers30min := CalculateTargetCustomers(Investment, 1800.0)
            RevenuePerSec := Customers30min * CustomerRevenueRate
            Efficiency := if(Investment > 0):
                RevenuePerSec / (Investment / 1000.0)
            else:
                0.0
            
            Print("Investment ${Investment}: Rate {CustomerRate}, Revenue/s ${RevenuePerSec}, Efficiency {Efficiency}")

# Create and run test
TestInstance := test_marketing_balance{}
TestInstance.TestBalance()
